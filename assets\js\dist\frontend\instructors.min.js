(()=>{"use strict";let t={},e=[];"undefined"!=typeof lpSkeletonParam&&(e=lpSkeletonParam);const n=(t,e=!1,n)=>{const s=((t,e)=>{const n=new URL(t);return Object.keys(e).forEach((t=>{n.searchParams.set(t,e[t])})),n})(urlListInstructorsAPI,t);fetch(s,{method:"GET"}).then((t=>t.json())).then((t=>{void 0!==t.data.content&&n&&n(t)})).catch((t=>{console.log(t)})).finally((()=>{if(!1===e){const e=lpInstructorsUrl+"?paged="+t.paged;window.history.pushState("","",e)}}))};!function(){let s="",a="";n({...e,paged:1},!0,(function(t){s=t.data.content,void 0!==t.data.pagination&&(a=t.data.pagination)}));let r=0;const o=setInterval((function(){r++,r>1e4&&clearInterval(o);const t=document.querySelector(".lp-list-instructors");if(t&&""!==s){clearInterval(o);const e=document.querySelector(".ul-list-instructors");t.classList.add("detected"),e.innerHTML=s,t.insertAdjacentHTML("beforeend",a)}}),1);document.addEventListener("click",(function(s){const a=s.target,r=a.closest(".lp-list-instructors");if(!r)return;const o=r.querySelector(".ul-list-instructors"),c=a.closest(".learn-press-pagination");if(!c||!r||!o)return;let i;if("a"===a.tagName.toLowerCase())i=a;else{if(!a.closest("a.page-numbers"))return;i=a.closest("a.page-numbers")}s.preventDefault();const l=parseInt(c.querySelector(".current").innerHTML);let u;u=i.classList.contains("next")?l+1:i.classList.contains("prev")?l-1:i.innerHTML,t={...t,paged:u,...e},n(t,!1,(function(t){o.innerHTML=t.data.content,c.remove(),void 0!==t.data.pagination&&r.insertAdjacentHTML("beforeend",t.data.pagination)}))}))}()})();