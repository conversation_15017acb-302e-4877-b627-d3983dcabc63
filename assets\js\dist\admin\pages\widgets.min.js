(()=>{const t=jQuery;function n(){t(".widget-content .sortable").sortable({handle:".drag",start(t,n){},update(n,e){const d=[];t(this).children().map((function(){d.push(t(this).find("input").val())})),d.join(",");const o=t(this).closest(".widget-content").find("input.fields-sort");o.val(d),o.trigger("change")},stop(t,n){}})}document.addEventListener("DOMContentLoaded",(function(e){document.querySelector("#widgets-editor")?t(document).on("widget-added",(function(t,n){})):t(document).on("learnpress/widgets/select",(function(){n()}))})),document.addEventListener("DOMContentLoaded",(function(t){n()})),t(document).on("widget-added widget-updated",(function(t){n()}))})();