(()=>{"use strict";const e=window.React,n=window.wp.i18n,s=window.wp.components,r=window.wp.blockEditor,t=t=>{const l=(0,r.useBlockProps)();return(0,e.createElement)("div",{...l},(0,e.createElement)(s.Placeholder,{label:(0,n.__)("Single Course (Legacy)","learnpress")},(0,e.createElement)("div",null,(0,n.__)("Display full content of Single Course, can not edit.","learnpress"))))},l=e=>null,o=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/single-course-legacy","title":"Single Course (Legacy)","category":"learnpress-legacy","description":"Renders template Single Course Legacy PHP templates.","textdomain":"learnpress","keywords":["legacy","learnpress"],"ancestor":[],"usesContext":[],"supports":{"align":true}}'),a=window.wp.blocks,c=window.wp.data;let i=null;var p,u,d;p=["learnpress/learnpress//single-lp_course"],u=o,d=e=>{(0,a.registerBlockType)(e.name,{...e,edit:t,save:l})},(0,c.subscribe)((()=>{const e={...u},n=(0,c.select)("core/editor")||null;if(!n||"function"!=typeof n.getCurrentPostId||!n.getCurrentPostId())return;const s=n.getCurrentPostId();null!==s&&i!==s&&(i=s,(0,a.getBlockType)(e.name)&&((0,a.unregisterBlockType)(e.name),p.includes(s)?(e.ancestor=null,d(e)):(e.ancestor||(e.ancestor=[]),d(e))))})),(0,a.registerBlockType)(o.name,{...o,edit:t,save:l})})();