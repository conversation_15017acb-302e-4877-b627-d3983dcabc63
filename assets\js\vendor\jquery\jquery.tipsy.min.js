!function(l){function a(t,i){return"function"==typeof t?t.call(i):t}function o(t,i){this.$element=l(t),this.options=i,this.enabled=!0,this.fixTitle()}o.prototype={show:function(){var t=this.getTitle();if(t&&this.enabled){var i=this.tip();i.find(".tipsy-inner")[this.options.html?"html":"text"](t),i[0].className="tipsy",i.remove().css({top:0,left:0,visibility:"hidden",display:"block"}).prependTo(document.body);var e,s=l.extend({},this.$element.offset(),{width:this.$element[0].offsetWidth,height:this.$element[0].offsetHeight}),n=i[0].offsetWidth,o=i[0].offsetHeight,t=a(this.options.gravity,this.$element[0]);switch(t.charAt(0)){case"n":e={top:s.top+s.height+this.options.offset,left:s.left+s.width/2-n/2};break;case"s":e={top:s.top-o-this.options.offset,left:s.left+s.width/2-n/2};break;case"e":e={top:s.top+s.height/2-o/2,left:s.left-n-this.options.offset};break;case"w":e={top:s.top+s.height/2-o/2,left:s.left+s.width+this.options.offset}}2==t.length&&("w"==t.charAt(1)?e.left=s.left+s.width/2-15:e.left=s.left+s.width/2-n+15),i.css(e).addClass("tipsy-"+t),i.find(".tipsy-arrow")[0].className="tipsy-arrow tipsy-arrow-"+t.charAt(0),this.options.className&&i.addClass(a(this.options.className,this.$element[0])),this.options.fade?i.stop().css({opacity:0,display:"block",visibility:"visible"}).animate({opacity:this.options.opacity}):i.css({visibility:"visible",opacity:this.options.opacity})}},hide:function(){this.options.fade?this.tip().stop().fadeOut(function(){l(this).remove()}):this.tip().remove()},fixTitle:function(){var t=this.$element;!t.attr("title")&&"string"==typeof t.attr("original-title")||t.attr("original-title",t.attr("title")||"").removeAttr("title")},getTitle:function(){var t,i=this.$element,e=this.options;return this.fixTitle(),"string"==typeof(e=this.options).title?t=i.attr("title"==e.title?"original-title":e.title):"function"==typeof e.title&&(t=e.title.call(i[0])),(t=(""+t).replace(/(^\s*|\s*$)/,""))||e.fallback},tip:function(){return this.$tip||(this.$tip=l('<div class="tipsy"></div>').html('<div class="tipsy-arrow"></div><div class="tipsy-inner"></div>'),this.$tip.data("tipsy-pointee",this.$element[0])),this.$tip},validate:function(){this.$element[0].parentNode||(this.hide(),this.$element=null,this.options=null)},enable:function(){this.enabled=!0},disable:function(){this.enabled=!1},toggleEnabled:function(){this.enabled=!this.enabled}},l.fn.tipsy=function(e){if(!0===e)return this.data("tipsy");if("string"!=typeof e)return(e=l.extend({},l.fn.tipsy.defaults,e)).live||this.each(function(){i(this)}),"manual"!=e.trigger&&(t=e.live?"live":"bind",s="hover"==e.trigger?"mouseenter":"focus",n="hover"==e.trigger?"mouseleave":"blur",this[t](s,function(){var t=i(this);t.hoverState="in",0==e.delayIn?t.show():(t.fixTitle(),setTimeout(function(){"in"==t.hoverState&&t.show()},e.delayIn))})[t](n,function(){var t=i(this);t.hoverState="out",0==e.delayOut?t.hide():setTimeout(function(){"out"==t.hoverState&&t.hide()},e.delayOut)})),this;function i(t){var i=l.data(t,"tipsy");return i||(i=new o(t,l.fn.tipsy.elementOptions(t,e)),l.data(t,"tipsy",i)),i}var t,s,n=this.data("tipsy");return n&&n[e](),this},l.fn.tipsy.defaults={className:null,delayIn:0,delayOut:0,fade:!1,fallback:"",gravity:"n",html:!1,live:!1,offset:0,opacity:.8,title:"title",trigger:"hover"},l.fn.tipsy.revalidate=function(){l(".tipsy").each(function(){var t=l.data(this,"tipsy-pointee");t&&function(t){for(;t=t.parentNode;)if(t==document)return 1}(t)||l(this).remove()})},l.fn.tipsy.elementOptions=function(t,i){return l.metadata?l.extend({},i,l(t).metadata()):i},l.fn.tipsy.autoNS=function(){return l(this).offset().top>l(document).scrollTop()+l(window).height()/2?"s":"n"},l.fn.tipsy.autoWE=function(){return l(this).offset().left>l(document).scrollLeft()+l(window).width()/2?"e":"w"},l.fn.tipsy.autoBounds=function(n,o){return function(){var t={ns:o[0],ew:1<o.length&&o[1]},i=l(document).scrollTop()+n,e=l(document).scrollLeft()+n,s=l(this);return s.offset().top<i&&(t.ns="n"),s.offset().left<e&&(t.ew="w"),l(window).width()+l(document).scrollLeft()-s.offset().left<n&&(t.ew="e"),l(window).height()+l(document).scrollTop()-s.offset().top<n&&(t.ns="s"),t.ns+(t.ew||"")}}}(jQuery);