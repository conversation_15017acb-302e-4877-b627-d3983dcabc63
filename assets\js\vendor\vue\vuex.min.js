!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self).Vuex=e()}(this,function(){"use strict";var u="undefined"!=typeof window&&window.__VUE_DEVTOOLS_GLOBAL_HOOK__;function a(e,n){Object.keys(e).forEach(function(t){return n(e[t],t)})}function f(t,e){if(!t)throw new Error("[vuex] "+e)}function i(t,e){this.runtime=e,this._children=Object.create(null),t=(this._rawModule=t).state,this.state=("function"==typeof t?t():t)||{}}var t={namespaced:{configurable:!0}};t.namespaced.get=function(){return!!this._rawModule.namespaced},i.prototype.addChild=function(t,e){this._children[t]=e},i.prototype.removeChild=function(t){delete this._children[t]},i.prototype.getChild=function(t){return this._children[t]},i.prototype.update=function(t){this._rawModule.namespaced=t.namespaced,t.actions&&(this._rawModule.actions=t.actions),t.mutations&&(this._rawModule.mutations=t.mutations),t.getters&&(this._rawModule.getters=t.getters)},i.prototype.forEachChild=function(t){a(this._children,t)},i.prototype.forEachGetter=function(t){this._rawModule.getters&&a(this._rawModule.getters,t)},i.prototype.forEachAction=function(t){this._rawModule.actions&&a(this._rawModule.actions,t)},i.prototype.forEachMutation=function(t){this._rawModule.mutations&&a(this._rawModule.mutations,t)},Object.defineProperties(i.prototype,t);function l(t){this.register([],t,!1)}l.prototype.get=function(t){return t.reduce(function(t,e){return t.getChild(e)},this.root)},l.prototype.getNamespace=function(t){var n=this.root;return t.reduce(function(t,e){return t+((n=n.getChild(e)).namespaced?e+"/":"")},"")},l.prototype.update=function(t){!function t(e,n,o){c(e,o);n.update(o);if(o.modules)for(var r in o.modules){if(!n.getChild(r))return void console.warn("[vuex] trying to add a new module '"+r+"' on hot reloading, manual reload is needed");t(e.concat(r),n.getChild(r),o.modules[r])}}([],this.root,t)},l.prototype.register=function(n,t,o){var r=this;void 0===o&&(o=!0),c(n,t);var e=new i(t,o);0===n.length?this.root=e:this.get(n.slice(0,-1)).addChild(n[n.length-1],e),t.modules&&a(t.modules,function(t,e){r.register(n.concat(e),t,o)})},l.prototype.unregister=function(t){var e=this.get(t.slice(0,-1)),t=t[t.length-1];e.getChild(t).runtime&&e.removeChild(t)};var d,e={assert:function(t){return"function"==typeof t},expected:"function"},s={getters:e,mutations:e,actions:{assert:function(t){return"function"==typeof t||"object"==typeof t&&"function"==typeof t.handler},expected:'function or object with "handler" function'}};function c(r,t){Object.keys(s).forEach(function(n){var o;t[n]&&(o=s[n],a(t[n],function(t,e){f(o.assert(t),function(t,e,n,o,r){n=e+" should be "+r+' but "'+e+"."+n+'"';0<t.length&&(n+=' in module "'+t.join(".")+'"');return n+=" is "+JSON.stringify(o)+"."}(r,n,e,t,o.expected))}))})}t=function t(e){var n=this;void 0===e&&(e={}),!d&&"undefined"!=typeof window&&window.Vue&&h(window.Vue),f(d,"must call Vue.use(Vuex) before creating a store instance."),f("undefined"!=typeof Promise,"vuex requires a Promise polyfill in this browser."),f(this instanceof t,"store must be called with the new operator.");var o=e.plugins;void 0===o&&(o=[]);var r=e.strict;void 0===r&&(r=!1),this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new l(e),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._watcherVM=new d;var i=this,s=this.dispatch,a=this.commit;this.dispatch=function(t,e){return s.call(i,t,e)},this.commit=function(t,e,n){return a.call(i,t,e,n)},this.strict=r;var c,r=this._modules.root.state;m(this,r,[],this._modules.root),p(this,r),o.forEach(function(t){return t(n)}),(void 0!==e.devtools?e:d.config).devtools&&(c=this,u&&((c._devtoolHook=u).emit("vuex:init",c),u.on("vuex:travel-to-state",function(t){c.replaceState(t)}),c.subscribe(function(t,e){u.emit("vuex:mutation",t,e)})))},e={state:{configurable:!0}};function n(e,n){return n.indexOf(e)<0&&n.push(e),function(){var t=n.indexOf(e);-1<t&&n.splice(t,1)}}function o(t,e){t._actions=Object.create(null),t._mutations=Object.create(null),t._wrappedGetters=Object.create(null),t._modulesNamespaceMap=Object.create(null);var n=t.state;m(t,n,[],t._modules.root,!0),p(t,n,e)}function p(n,t,e){var o=n._vm;n.getters={};var r=n._wrappedGetters,i={};a(r,function(t,e){i[e]=function(){return t(n)},Object.defineProperty(n.getters,e,{get:function(){return n._vm[e]},enumerable:!0})});var s,r=d.config.silent;d.config.silent=!0,n._vm=new d({data:{$$state:t},computed:i}),d.config.silent=r,n.strict&&(s=n)._vm.$watch(function(){return this._data.$$state},function(){f(s._committing,"do not mutate vuex store state outside mutation handlers.")},{deep:!0,sync:!0}),o&&(e&&n._withCommit(function(){o._data.$$state=null}),d.nextTick(function(){return o.$destroy()}))}function m(i,n,o,t,r){var e,s,a=!o.length,c=i._modules.getNamespace(o);t.namespaced&&(i._modulesNamespaceMap[c]=t),a||r||(e=y(n,o.slice(0,-1)),s=o[o.length-1],i._withCommit(function(){d.set(e,s,t.state)}));var u,f,l,p,h=t.context=(u=i,l=o,a={dispatch:(p=""===(f=c))?u.dispatch:function(t,e,n){var o=v(t,e,n),t=o.payload,e=o.options,n=o.type;if(e&&e.root||(n=f+n,u._actions[n]))return u.dispatch(n,t);console.error("[vuex] unknown local action type: "+o.type+", global type: "+n)},commit:p?u.commit:function(t,e,n){var o=v(t,e,n),t=o.payload,e=o.options,n=o.type;e&&e.root||(n=f+n,u._mutations[n])?u.commit(n,t,e):console.error("[vuex] unknown local mutation type: "+o.type+", global type: "+n)}},Object.defineProperties(a,{getters:{get:p?function(){return u.getters}:function(){return n=u,r={},i=(o=f).length,Object.keys(n.getters).forEach(function(t){var e;t.slice(0,i)===o&&(e=t.slice(i),Object.defineProperty(r,e,{get:function(){return n.getters[t]},enumerable:!0}))}),r;var n,o,r,i}},state:{get:function(){return y(u.state,l)}}}),a);t.forEachMutation(function(t,e){var n,o,r;e=c+e,o=t,r=h,((n=i)._mutations[e]||(n._mutations[e]=[])).push(function(t){o.call(n,r.state,t)})}),t.forEachAction(function(t,e){var n,o,r,e=t.root?e:c+e,t=t.handler||t;e=e,o=t,r=h,((n=i)._actions[e]||(n._actions[e]=[])).push(function(t,e){t=o.call(n,{dispatch:r.dispatch,commit:r.commit,getters:r.getters,state:r.state,rootGetters:n.getters,rootState:n.state},t,e);return(e=t)&&"function"==typeof e.then||(t=Promise.resolve(t)),n._devtoolHook?t.catch(function(t){throw n._devtoolHook.emit("vuex:error",t),t}):t})}),t.forEachGetter(function(t,e){!function(t,e,n,o){if(t._wrappedGetters[e])return console.error("[vuex] duplicate getter key: "+e);t._wrappedGetters[e]=function(t){return n(o.state,o.getters,t.state,t.getters)}}(i,c+e,t,h)}),t.forEachChild(function(t,e){m(i,n,o.concat(e),t,r)})}function y(t,e){return e.length?e.reduce(function(t,e){return t[e]},t):t}function v(t,e,n){var o;return null!==(o=t)&&"object"==typeof o&&t.type&&(n=e,t=(e=t).type),f("string"==typeof t,"expects string as the type, but found "+typeof t+"."),{type:t,payload:e,options:n}}function h(t){var e;function n(){var t=this.$options;t.store?this.$store="function"==typeof t.store?t.store():t.store:t.parent&&t.parent.$store&&(this.$store=t.parent.$store)}d&&t===d?console.error("[vuex] already installed. Vue.use(Vuex) should be called only once."):(t=d=t,2<=Number(t.version.split(".")[0])?t.mixin({beforeCreate:n}):(e=t.prototype._init,t.prototype._init=function(t){void 0===t&&(t={}),t.init=t.init?[n].concat(t.init):n,e.call(this,t)}))}e.state.get=function(){return this._vm._data.$$state},e.state.set=function(t){f(!1,"use store.replaceState() to explicit replace store state.")},t.prototype.commit=function(t,e,n){var o=this,e=v(t,e,n),n=e.type,r=e.payload,e=e.options,i={type:n,payload:r},s=this._mutations[n];s?(this._withCommit(function(){s.forEach(function(t){t(r)})}),this._subscribers.forEach(function(t){return t(i,o.state)}),e&&e.silent&&console.warn("[vuex] mutation type: "+n+". Silent option has been removed. Use the filter functionality in the vue-devtools")):console.error("[vuex] unknown mutation type: "+n)},t.prototype.dispatch=function(t,e){var n=this,t=v(t,e),e=t.type,o=t.payload,r={type:e,payload:o},t=this._actions[e];if(t){try{this._actionSubscribers.filter(function(t){return t.before}).forEach(function(t){return t.before(r,n.state)})}catch(t){console.warn("[vuex] error in before action subscribers: "),console.error(t)}return(1<t.length?Promise.all(t.map(function(t){return t(o)})):t[0](o)).then(function(t){try{n._actionSubscribers.filter(function(t){return t.after}).forEach(function(t){return t.after(r,n.state)})}catch(t){console.warn("[vuex] error in after action subscribers: "),console.error(t)}return t})}console.error("[vuex] unknown action type: "+e)},t.prototype.subscribe=function(t){return n(t,this._subscribers)},t.prototype.subscribeAction=function(t){return n("function"==typeof t?{before:t}:t,this._actionSubscribers)},t.prototype.watch=function(t,e,n){var o=this;return f("function"==typeof t,"store.watch only accepts a function."),this._watcherVM.$watch(function(){return t(o.state,o.getters)},e,n)},t.prototype.replaceState=function(t){var e=this;this._withCommit(function(){e._vm._data.$$state=t})},t.prototype.registerModule=function(t,e,n){void 0===n&&(n={}),"string"==typeof t&&(t=[t]),f(Array.isArray(t),"module path must be a string or an Array."),f(0<t.length,"cannot register the root module by using registerModule."),this._modules.register(t,e),m(this,this.state,t,this._modules.get(t),n.preserveState),p(this,this.state)},t.prototype.unregisterModule=function(e){var n=this;"string"==typeof e&&(e=[e]),f(Array.isArray(e),"module path must be a string or an Array."),this._modules.unregister(e),this._withCommit(function(){var t=y(n.state,e.slice(0,-1));d.delete(t,e[e.length-1])}),o(this)},t.prototype.hotUpdate=function(t){this._modules.update(t),o(this,!0)},t.prototype._withCommit=function(t){var e=this._committing;this._committing=!0,t(),this._committing=e},Object.defineProperties(t.prototype,e);var r=x(function(r,t){var n={};return w(t).forEach(function(t){var e=t.key,o=t.val;n[e]=function(){var t=this.$store.state,e=this.$store.getters;if(r){var n=$(this.$store,"mapState",r);if(!n)return;t=n.context.state,e=n.context.getters}return"function"==typeof o?o.call(this,t,e):t[o]},n[e].vuex=!0}),n}),g=x(function(i,t){var n={};return w(t).forEach(function(t){var e=t.key,r=t.val;n[e]=function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];var n=this.$store.commit;if(i){var o=$(this.$store,"mapMutations",i);if(!o)return;n=o.context.commit}return"function"==typeof r?r.apply(this,[n].concat(t)):n.apply(this.$store,[r].concat(t))}}),n}),_=x(function(o,t){var r={};return w(t).forEach(function(t){var e=t.key,n=t.val,n=o+n;r[e]=function(){if(!o||$(this.$store,"mapGetters",o)){if(n in this.$store.getters)return this.$store.getters[n];console.error("[vuex] unknown getter: "+n)}},r[e].vuex=!0}),r}),b=x(function(i,t){var n={};return w(t).forEach(function(t){var e=t.key,r=t.val;n[e]=function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];var n=this.$store.dispatch;if(i){var o=$(this.$store,"mapActions",i);if(!o)return;n=o.context.dispatch}return"function"==typeof r?r.apply(this,[n].concat(t)):n.apply(this.$store,[r].concat(t))}}),n});function w(e){return Array.isArray(e)?e.map(function(t){return{key:t,val:t}}):Object.keys(e).map(function(t){return{key:t,val:e[t]}})}function x(n){return function(t,e){return"string"!=typeof t?(e=t,t=""):"/"!==t.charAt(t.length-1)&&(t+="/"),n(t,e)}}function $(t,e,n){t=t._modulesNamespaceMap[n];return t||console.error("[vuex] module namespace not found in "+e+"(): "+n),t}return{Store:t,install:h,version:"3.1.0",mapState:r,mapMutations:g,mapGetters:_,mapActions:b,createNamespacedHelpers:function(t){return{mapState:r.bind(null,t),mapGetters:_.bind(null,t),mapMutations:g.bind(null,t),mapActions:b.bind(null,t)}}}});