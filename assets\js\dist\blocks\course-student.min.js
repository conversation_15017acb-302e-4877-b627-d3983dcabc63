(()=>{"use strict";const e=window.React,t=window.wp.i18n,n=window.wp.blockEditor,s=window.wp.components,r=(window.wp.element,r=>{const a=(0,n.useBlockProps)(),{attributes:l,setAttributes:o,context:i}=r,{lpCourseData:c}=i,u=c?.student||'<div class="course-count-student"><div class="course-count-student">3 Student</div></div>';return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(n.InspectorControls,null,(0,e.createElement)(s.PanelB<PERSON>,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(s.ToggleControl,{label:(0,t.__)("Show Label","learnpress"),checked:l.showLabel,onChange:e=>{o({showLabel:e})}}),(0,e.createElement)(s.<PERSON><PERSON>,{label:(0,t.__)("Show Icon","learnpress"),checked:l.showIcon,onChange:e=>{o({showIcon:e})}}))),(0,e.createElement)("div",{...a},(0,e.createElement)("div",{className:"info-meta-item"},(0,e.createElement)("span",{className:"info-meta-left"},l.showIcon&&(0,e.createElement)("span",{dangerouslySetInnerHTML:{__html:'<i class="lp-icon-user-graduate"></i>'}}),l.showLabel?"Student:":""),(0,e.createElement)("span",{className:"info-meta-right",dangerouslySetInnerHTML:{__html:u}}))))}),a=e=>null,l=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-student","title":"Course Student Count","category":"learnpress-course-elements","description":"Show number students attempt course.","textdomain":"learnpress","keywords":["count","student","learnpress"],"icon":"admin-users","ancestor":["learnpress/single-course","learnpress/course-item-template"],"usesContext":["lpCourseData"],"attributes":{"showIcon":{"type":"boolean","default":true},"showLabel":{"type":"boolean","default":true}},"supports":{"align":["wide","full"],"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),o=window.wp.blocks,i=window.wp.data;let c=null;var u,p,d;u=["learnpress/learnpress//single-lp_course"],p=l,d=e=>{(0,o.registerBlockType)(e.name,{...e,edit:r,save:a})},(0,i.subscribe)((()=>{const e={...p},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const n=t.getCurrentPostId();null!==n&&c!==n&&(c=n,(0,o.getBlockType)(e.name)&&((0,o.unregisterBlockType)(e.name),u.includes(n)?(e.ancestor=null,d(e)):(e.ancestor||(e.ancestor=[]),d(e))))})),(0,o.registerBlockType)(l.name,{...l,edit:r,save:a})})();