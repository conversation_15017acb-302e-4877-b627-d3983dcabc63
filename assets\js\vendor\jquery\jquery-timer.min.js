!function(n){n.backward_timer=function(t){var e={seconds:5,step:1,format:"h%:m%:s%",value_setter:void 0,on_exhausted:function(t){},on_tick:function(t){}},s=this;s.seconds_left=0,s.target=n(t),s.timeout=void 0,s.settings={},s.methods={init:function(t){s.settings=n.extend({},e,t),null==s.settings.value_setter&&(s.target.is("input")?s.settings.value_setter="val":s.settings.value_setter="text"),s.methods.reset()},start:function(){var t;null==s.timeout&&(t=s.seconds_left==s.settings.seconds?0:1e3*s.settings.step,setTimeout(s.methods._on_tick,t,t))},cancel:function(){null!=s.timeout&&(clearTimeout(s.timeout),s.timeout=void 0)},reset:function(){s.seconds_left=s.settings.seconds,s.methods._render_seconds()},_on_tick:function(t){0!=t&&s.settings.on_tick(s),s.methods._render_seconds(),0<s.seconds_left?(t=s.seconds_left<s.settings.step?s.seconds_left:s.settings.step,s.seconds_left-=t,t=1e3*t,s.timeout=setTimeout(s.methods._on_tick,t,t)):(s.timeout=void 0,s.settings.on_exhausted(s))},_render_seconds:function(){var t=s.methods._seconds_to_dhms(s.seconds_left),e=s.settings.format;e=(e=-1!==e.indexOf("d%")?e.replace("d%",t.d).replace("h%",s.methods._check_leading_zero(t.h)):e.replace("h%",24*t.d+t.h)).replace("m%",s.methods._check_leading_zero(t.m)).replace("s%",s.methods._check_leading_zero(t.s)),s.target[s.settings.value_setter](e)},_seconds_to_dhms:function(t){var e=Math.floor(t/86400),t=t-24*e*3600,s=Math.floor(t/3600),t=t-3600*s,n=Math.floor(t/60);return{d:e,h:s,m:n,s:Math.floor(t-60*n)}},_check_leading_zero:function(t){return t<10?"0"+t:""+t}}},n.fn.backward_timer=function(e){var s=arguments;return this.each(function(){var t=n(this).data("backward_timer");return null==t&&(t=new n.backward_timer(this),n(this).data("backward_timer",t)),t.methods[e]?t.methods[e].apply(this,Array.prototype.slice.call(s,1)):"object"!=typeof e&&e?void n.error("Method "+e+" does not exist on jQuery.backward_timer"):t.methods.init.apply(this,s)})}}(jQuery);