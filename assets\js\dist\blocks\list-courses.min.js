(()=>{"use strict";const e=window.React,a=window.wp.i18n,t=window.wp.components,l=window.wp.blockEditor,r=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/list-courses","title":"Course Listing","category":"learnpress-category","description":"Course Listing block","textdomain":"learnpress","keywords":["listing course","learnpress","query loop"],"usesContext":[],"attributes":{"courseQuery":{"type":"object","default":{"limit":10,"order_by":"post_date","related":false,"pagination":false,"pagination_type":"number","tag_id":"","term_id":"","load_ajax":false}}},"providesContext":{"lpCourseQuery":"courseQuery"},"supports":{"html":false,"align":["wide","full"]}}');(0,window.wp.blocks.registerBlockType)("learnpress/list-courses",{...r,edit:r=>{const n=(0,l.useBlockProps)(),{attributes:o,setAttributes:s,clientId:i}=r,{courseQuery:p}=o,u=[{label:(0,a.__)("Number","learnpress"),value:"number"},{label:(0,a.__)("Load more","learnpress"),value:"load-more"},{label:(0,a.__)("Infinite Scroll","learnpress"),value:"infinite"}];return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.InspectorControls,null,(0,e.createElement)(t.PanelBody,{title:(0,a.__)("Query Settings","learnpress")},(0,e.createElement)(t.RangeControl,{label:(0,a.__)("Posts per page"),value:p.limit,onChange:e=>s({courseQuery:{...p,limit:e}}),min:1,max:20}),(0,e.createElement)(t.ToggleControl,{label:(0,a.__)("Related Course","learnpress"),checked:p.related,onChange:e=>{s(e?{courseQuery:{...p,term_id:"",tag_id:"",pagination:!1,order_by:"post_date",related:e}}:{courseQuery:{...p,related:e}})}}),(0,e.createElement)(t.ToggleControl,{label:(0,a.__)("Enable AJAX","learnpress"),checked:p.load_ajax,onChange:e=>{s({courseQuery:{...p,load_ajax:e,load_ajax_after:!!e&&p.load_ajax_after}})}}),!p.related&&(0,e.createElement)(t.SelectControl,{label:(0,a.__)("Order by","learnpress"),value:p.order_by,options:[{label:"Newly published",value:"post_date"},{label:"Title a-z",value:"post_title"},{label:"Title z-a",value:"post_title_desc"},{label:"Price high to low",value:"price"},{label:"Price low to high",value:"price_low"},{label:"Popular",value:"popular"},{label:"Average Ratings",value:"rating"}],onChange:e=>{s({courseQuery:{...p,order_by:e}})}}),!p.related&&(0,e.createElement)(t.ToggleControl,{label:(0,a.__)("Pagination","learnpress"),checked:p.pagination,onChange:e=>{s({courseQuery:{...p,pagination:e,pagination_type:e?p.pagination_type:"number"}})}}),p.pagination&&(0,e.createElement)(t.SelectControl,{label:(0,a.__)("Pagination Type","learnpress"),value:p.pagination_type,options:u,onChange:e=>{s({courseQuery:{...p,pagination_type:e}})}}))),(0,e.createElement)("div",{...n},(0,e.createElement)(l.InnerBlocks,{template:[["learnpress/course-item-template"]]})))},save:a=>{const t=l.useBlockProps.save();return(0,e.createElement)("div",{...t},(0,e.createElement)(l.InnerBlocks.Content,null))}})})();