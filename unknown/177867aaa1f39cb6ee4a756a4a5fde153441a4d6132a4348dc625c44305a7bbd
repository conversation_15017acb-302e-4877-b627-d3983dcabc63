{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "learnpress/course-author-filter", "title": "Author <PERSON><PERSON>", "category": "learnpress-course-elements", "description": "Renders template Author Fi<PERSON> templates.", "textdomain": "learnpress", "keywords": ["filter", "learnpress"], "ancestor": ["learnpress/course-filter"], "usesContext": [], "supports": {"multiple": false, "align": ["wide", "full"], "html": false, "typography": {"fontSize": true, "lineHeight": false, "fontWeight": true, "__experimentalFontFamily": false, "__experimentalTextDecoration": false, "__experimentalFontStyle": false, "__experimentalFontWeight": true, "__experimentalLetterSpacing": false, "__experimentalTextTransform": true, "__experimentalDefaultControls": {"fontSize": true}}, "color": {"background": false, "text": true, "__experimentalDefaultControls": {"text": true}}, "__experimentalBorder": {"color": true, "radius": false, "width": true, "__experimentalDefaultControls": {"width": false, "color": false}}, "spacing": {"padding": true, "margin": true, "__experimentalDefaultControls": {"padding": false, "margin": false}}}}