(()=>{"use strict";const e=window.React,t=window.wp.i18n,r=window.wp.blockEditor,l=window.wp.components,s=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-filter","title":"Course Filter","category":"learnpress-category","description":"Show filter fields for courses.","textdomain":"learnpress","keywords":["filter","learnpress"],"icon":"filter","usesContext":[],"attributes":{"title":{"type":"string","default":"Course Filter"},"numberLevelCategory":{"type":"number","default":1},"showInRest":{"type":"boolean","default":true},"hideCountZero":{"type":"boolean","default":true},"searchSuggestion":{"type":"boolean","default":true}},"supports":{"inserter":true,"reusable":true,"reorder":true,"html":false,"multiple":true,"wrapper":false}}');(0,window.wp.blocks.registerBlockType)("learnpress/course-filter",{...s,edit:s=>{var n,o;const a=(0,r.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(r.InspectorControls,null,(0,e.createElement)(l.PanelBody,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(l.TextControl,{label:(0,t.__)("Title","learnpress"),onChange:e=>{s.setAttributes({title:null!=e?e:""})},value:null!==(n=s.attributes.title)&&void 0!==n?n:"Course Filter"}),(0,e.createElement)(l.TextControl,{label:(0,t.__)("Level of category to display on Frontend","learnpress"),type:"number",min:"1",onChange:e=>{s.setAttributes({numberLevelCategory:e?parseInt(e,10):1})},value:null!==(o=s.attributes.numberLevelCategory)&&void 0!==o?o:1}),(0,e.createElement)(l.ToggleControl,{label:(0,t.__)("Load widget via REST","learnpress"),checked:!!s.attributes.showInRest,onChange:e=>{s.setAttributes({showInRest:!!e})}}),(0,e.createElement)(l.ToggleControl,{label:(0,t.__)("Hide field has count is zero","learnpress"),checked:!!s.attributes.hideCountZero,onChange:e=>{s.setAttributes({hideCountZero:!!e})}}),(0,e.createElement)(l.ToggleControl,{label:(0,t.__)("Enable Keyword Search Suggestion","learnpress"),checked:!!s.attributes.searchSuggestion,onChange:e=>{s.setAttributes({searchSuggestion:!!e})}}))),(0,e.createElement)("div",{...a},(0,e.createElement)("div",{className:"filter"},(0,e.createElement)("h3",null,s.attributes.title)),(0,e.createElement)("div",null,(0,e.createElement)(r.InnerBlocks,{allowedBlocks:["learnpress/course-search-filter","learnpress/course-author-filter","learnpress/course-level-filter","learnpress/course-price-filter","learnpress/course-categories-filter","learnpress/course-tag-filter","learnpress/course-review-filter","learnpress/button-submit-filter","learnpress/button-reset-filter"]}))))},save:t=>(0,e.createElement)(e.Fragment,null,(0,e.createElement)(r.InnerBlocks.Content,null))})})();