"use strict";var _extends=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n,o=arguments[e];for(n in o)Object.prototype.hasOwnProperty.call(o,n)&&(t[n]=o[n])}return t};function _toConsumableArray(t){if(Array.isArray(t)){for(var e=0,n=Array(t.length);e<t.length;e++)n[e]=t[e];return n}return Array.from(t)}!function(){function t(o){function n(t){t.parentElement.removeChild(t)}function i(t,e,n){n=0===n?t.children[0]:t.children[n-1].nextSibling;t.insertBefore(e,n)}function r(t,e){var n=this;this.$nextTick(function(){return n.$emit(t.toLowerCase(),e)})}var s=["Start","Add","Remove","Update","End"],a=["Choose","Sort","Filter","Clone"],l=["Move"].concat(s,a).map(function(t){return"on"+t}),u=null;return{name:"draggable",props:{options:Object,list:{type:Array,required:!1,default:null},value:{type:Array,required:!1,default:null},noTransitionOnDrag:{type:Boolean,default:!1},clone:{type:Function,default:function(t){return t}},element:{type:String,default:"div"},move:{type:Function,default:null}},data:function(){return{transitionMode:!1,componentMode:!1}},render:function(t){var e=this.$slots.default;!e||1!==e.length||(o=e[0]).componentOptions&&"transition-group"===o.componentOptions.tag&&(this.transitionMode=!0);var n=e,o=this.$slots.footer;return o&&(n=e?[].concat(_toConsumableArray(e),_toConsumableArray(o)):[].concat(_toConsumableArray(o))),t(this.element,null,n)},mounted:function(){var n=this;if(this.componentMode=this.element.toLowerCase()!==this.$el.nodeName.toLowerCase(),this.componentMode&&this.transitionMode)throw new Error("Transition-group inside component is not supported. Please alter element value or remove transition-group. Current element value: "+this.element);var e={};s.forEach(function(t){e["on"+t]=function(e){var n=this;return function(t){null!==n.realList&&n["onDrag"+e](t),r.call(n,e,t)}}.call(n,t)}),a.forEach(function(t){e["on"+t]=r.bind(n,t)});var t=_extends({},this.options,e,{onMove:function(t,e){return n.onDragMove(t,e)}});"draggable"in t||(t.draggable=">*"),this._sortable=new o(this.rootContainer,t),this.computeIndexes()},beforeDestroy:function(){this._sortable.destroy()},computed:{rootContainer:function(){return this.transitionMode?this.$el.children[0]:this.$el},isCloning:function(){return!!this.options&&!!this.options.group&&"clone"===this.options.group.pull},realList:function(){return this.list||this.value}},watch:{options:{handler:function(t){for(var e in t)-1==l.indexOf(e)&&this._sortable.option(e,t[e])},deep:!0},realList:function(){this.computeIndexes()}},methods:{getChildrenNodes:function(){if(this.componentMode)return this.$children[0].$slots.default;var t=this.$slots.default;return this.transitionMode?t[0].child.$slots.default:t},computeIndexes:function(){var t=this;this.$nextTick(function(){t.visibleIndexes=function(t,e,n){if(!t)return[];var o=t.map(function(t){return t.elm}),e=[].concat(_toConsumableArray(e)).map(function(t){return o.indexOf(t)});return n?e.filter(function(t){return-1!==t}):e}(t.getChildrenNodes(),t.rootContainer.children,t.transitionMode)})},getUnderlyingVm:function(t){var e,t=(e=this.getChildrenNodes()||[],t=t,e.map(function(t){return t.elm}).indexOf(t));return-1===t?null:{index:t,element:this.realList[t]}},getUnderlyingPotencialDraggableComponent:function(t){t=t.__vue__;return t&&t.$options&&"transition-group"===t.$options._componentTag?t.$parent:t},emitChanges:function(t){var e=this;this.$nextTick(function(){e.$emit("change",t)})},alterList:function(t){this.list?t(this.list):(t(t=[].concat(_toConsumableArray(this.value))),this.$emit("input",t))},spliceList:function(){function t(t){return t.splice.apply(t,e)}var e=arguments;this.alterList(t)},updatePosition:function(e,n){function t(t){return t.splice(n,0,t.splice(e,1)[0])}this.alterList(t)},getRelatedContextFromMoveEvent:function(t){var e=t.to,n=t.related,o=this.getUnderlyingPotencialDraggableComponent(e);if(!o)return{component:o};var i=o.realList,t={list:i,component:o};if(e!==n&&i&&o.getUnderlyingVm){n=o.getUnderlyingVm(n);if(n)return _extends(n,t)}return t},getVmIndex:function(t){var e=this.visibleIndexes,n=e.length;return n-1<t?n:e[t]},getComponent:function(){return this.$slots.default[0].componentInstance},resetTransitionData:function(t){this.noTransitionOnDrag&&this.transitionMode&&(this.getChildrenNodes()[t].data=null,(t=this.getComponent()).children=[],t.kept=void 0)},onDragStart:function(t){this.context=this.getUnderlyingVm(t.item),t.item._underlying_vm_=this.clone(this.context.element),u=t.item},onDragAdd:function(t){var e=t.item._underlying_vm_;void 0!==e&&(n(t.item),t=this.getVmIndex(t.newIndex),this.spliceList(t,0,e),this.computeIndexes(),t={element:e,newIndex:t},this.emitChanges({added:t}))},onDragRemove:function(t){var e;i(this.rootContainer,t.item,t.oldIndex),this.isCloning?n(t.clone):(e=this.context.index,this.spliceList(e,1),t={element:this.context.element,oldIndex:e},this.resetTransitionData(e),this.emitChanges({removed:t}))},onDragUpdate:function(t){n(t.item),i(t.from,t.item,t.oldIndex);var e=this.context.index,t=this.getVmIndex(t.newIndex);this.updatePosition(e,t);t={element:this.context.element,oldIndex:e,newIndex:t};this.emitChanges({moved:t})},computeFutureIndex:function(t,e){if(!t.element)return 0;var n=[].concat(_toConsumableArray(e.to.children)).filter(function(t){return"none"!==t.style.display}),o=n.indexOf(e.related),o=t.component.getVmIndex(o);return-1!=n.indexOf(u)||!e.willInsertAfter?o:o+1},onDragMove:function(t,e){var n=this.move;if(!n||!this.realList)return!0;var o=this.getRelatedContextFromMoveEvent(t),i=this.context,r=this.computeFutureIndex(o,t);return _extends(i,{futureIndex:r}),_extends(t,{relatedContext:o,draggedContext:i}),n(t,e)},onDragEnd:function(){this.computeIndexes(),u=null}}}}var e;Array.from||(Array.from=function(t){return[].slice.call(t)}),"object"==typeof exports||("function"==typeof define&&define.amd?define(["sortablejs"],t):window&&window.Vue&&window.Sortable&&(e=t(window.Sortable),Vue.component("draggable",e)))}();