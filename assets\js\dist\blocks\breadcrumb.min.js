(()=>{"use strict";const e=window.React,t=window.wp.i18n,l=window.wp.blockEditor,r=window.wp.components,a=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/breadcrumb","title":"Learnpress Breadcrumb","category":"learnpress-category","icon":"ellipsis","description":"Renders Learnpress Breadcrumb PHP Template.","textdomain":"learnpress","keywords":["learnpress breadcrumb","learnpress"],"usesContext":[],"attributes":{"showHome":{"type":"boolean","default":true},"homeLabel":{"type":"string","default":"Home"}},"supports":{"multiple":false,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":true,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"text":true,"link":true,"background":false,"__experimentalDefaultControls":{"text":true,"link":false}}}}');(0,window.wp.blocks.registerBlockType)(a.name,{...a,edit:a=>{var n;const s=(0,l.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.InspectorControls,null,(0,e.createElement)(r.PanelBody,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(r.ToggleControl,{label:(0,t.__)("Show Home","learnpress"),checked:!!a.attributes.showHome,onChange:e=>a.setAttributes({showHome:!!e})}),a.attributes.showHome?(0,e.createElement)(r.TextControl,{label:(0,t.__)("Home Label","learnpress"),onChange:e=>{a.setAttributes({homeLabel:null!=e?e:"Home"})},value:null!==(n=a.attributes.homeLabel)&&void 0!==n?n:"Home"}):"")),(0,e.createElement)("div",{...s},(0,e.createElement)("ul",{className:"learn-press-breadcrumb"},a.attributes.showHome?(0,e.createElement)(e.Fragment,null,(0,e.createElement)("li",null,(0,e.createElement)("a",null,a.attributes.homeLabel)),(0,e.createElement)("li",{className:"breadcrumb-delimiter"},(0,e.createElement)("i",{className:"lp-icon-angle-right"}))):"",(0,e.createElement)("li",null,(0,e.createElement)("a",null,(0,t.__)("Navigation","learnpress"))),(0,e.createElement)("li",{className:"breadcrumb-delimiter"},(0,e.createElement)("i",{className:"lp-icon-angle-right"})),(0,e.createElement)("li",null,(0,e.createElement)("span",null,(0,t.__)("Path","learnpress"))))))},save:e=>null})})();