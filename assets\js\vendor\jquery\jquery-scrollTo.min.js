!function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery"],e):"undefined"!=typeof module&&module.exports?module.exports=e(require("jquery")):e(jQuery)}(function(r){"use strict";var w=r.scrollTo=function(e,t,o){return r(window).scrollTo(e,t,o)};function i(e){return!e.nodeName||-1!==r.inArray(e.nodeName.toLowerCase(),["iframe","#document","html","body"])}function t(e){return r.isFunction(e)||r.isPlainObject(e)?e:{top:e,left:e}}return w.defaults={axis:"xy",duration:0,limit:!0},r.fn.scrollTo=function(e,o,h){"object"==typeof o&&(h=o,o=0),"function"==typeof h&&(h={onAfter:h}),"max"===e&&(e=9e9),h=r.extend({},w.defaults,h),o=o||h.duration;var x=h.queue&&1<h.axis.length;return x&&(o/=2),h.offset=t(h.offset),h.over=t(h.over),this.each(function(){if(null!==e){var u,a=i(this),f=a?this.contentWindow||window:this,c=r(f),l=e,d={};switch(typeof l){case"number":case"string":if(/^([+-]=?)?\d+(\.\d+)?(px|%)?$/.test(l)){l=t(l);break}l=a?r(l):r(l,f);case"object":if(0===l.length)return;(l.is||l.style)&&(u=(l=r(l)).offset())}var m=r.isFunction(h.offset)&&h.offset(f,l)||h.offset;r.each(h.axis.split(""),function(e,t){var o="x"===t?"Left":"Top",n=o.toLowerCase(),r="scroll"+o,i=c[r](),s=w.max(f,t);u?(d[r]=u[n]+(a?0:i-c.offset()[n]),h.margin&&(d[r]-=parseInt(l.css("margin"+o),10)||0,d[r]-=parseInt(l.css("border"+o+"Width"),10)||0),d[r]+=m[n]||0,h.over[n]&&(d[r]+=l["x"===t?"width":"height"]()*h.over[n])):(n=l[n],d[r]=n.slice&&"%"===n.slice(-1)?parseFloat(n)/100*s:n),h.limit&&/^\d+$/.test(d[r])&&(d[r]=d[r]<=0?0:Math.min(d[r],s)),!e&&1<h.axis.length&&(i===d[r]?d={}:x&&(p(h.onAfterFirst),d={}))}),p(h.onAfter)}function p(e){var t=r.extend({},h,{queue:!0,duration:o,complete:e&&function(){e.call(f,l,h)}});c.animate(d,t)}})},w.max=function(e,t){var o="x"===t?"Width":"Height",n="scroll"+o;if(!i(e))return e[n]-r(e)[o.toLowerCase()]();t="client"+o,o=e.ownerDocument||e.document,e=o.documentElement,o=o.body;return Math.max(e[n],o[n])-Math.min(e[t],o[t])},r.Tween.propHooks.scrollLeft=r.Tween.propHooks.scrollTop={get:function(e){return r(e.elem)[e.prop]()},set:function(e){var t=this.get(e);if(e.options.interrupt&&e._last&&e._last!==t)return r(e.elem).stop();var o=Math.round(e.now);t!==o&&(r(e.elem)[e.prop](o),e._last=this.get(e))}},w});