(()=>{"use strict";const e=window.React,t=window.wp.i18n,r=window.wp.blockEditor,a=a=>{const n=(0,r.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...n},(0,e.createElement)("div",{className:"course-target extra-box"},(0,e.createElement)("h3",{className:"extra-box__title"},(0,t.__)("Target audiences","learnpress")),(0,e.createElement)("ul",null,(0,e.createElement)("li",null,"Mavis tolluntur redargueret spe fortior ames amicitia petitur cariorem similiora gaudeant"),(0,e.createElement)("li",null,"Fuisse confirmandus materiam reges versuta improbos inconstantissime rationis antiocho stultorum sequetur dicimus emolumento video hanc"),(0,e.createElement)("li",null,"Perfecit exquisita urbe asoti discere decimum existeret lyco morbo hi")))))},n=e=>null,s=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-target-audiences","title":"Course Target audiences","category":"learnpress-course-elements","icon":"index-card","description":"Renders template Box Extra Target audiences Course PHP templates.","textdomain":"learnpress","keywords":["box extra target audiences single course","learnpress"],"ancestor":["learnpress/single-course"],"usesContext":[],"supports":{"multiple":false,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"heading":true,"gradients":false,"__experimentalDefaultControls":{"text":true,"h3":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),l=window.wp.blocks,i=window.wp.data;let o=null;var u,c,m;u=["learnpress/learnpress//single-lp_course"],c=s,m=e=>{(0,l.registerBlockType)(e.name,{...e,edit:a,save:n})},(0,i.subscribe)((()=>{const e={...c},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&o!==r&&(o=r,(0,l.getBlockType)(e.name)&&((0,l.unregisterBlockType)(e.name),u.includes(r)?(e.ancestor=null,m(e)):(e.ancestor||(e.ancestor=[]),m(e))))})),(0,l.registerBlockType)(s.name,{...s,edit:a,save:n})})();