(()=>{"use strict";const e=window.React,t=(window.wp.i18n,window.wp.blockEditor),r=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-search","title":"Course Search","category":"learnpress-course-elements","icon":"search","description":"Show Search Course.","textdomain":"learnpress","keywords":["search","learnpress"],"ancestor":["learnpress/list-courses"],"usesContext":[],"supports":{"multiple":true,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":false,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}}}}');(0,window.wp.blocks.registerBlockType)(r.name,{...r,edit:r=>{const s=(0,t.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...s},(0,e.createElement)("form",{className:"block-search-courses"},(0,e.createElement)("input",{type:"search",placeholder:"Search courses...",name:"c_search",value:""}),(0,e.createElement)("button",{type:"submit",disabled:!0,name:"lp-btn-search-courses"},(0,e.createElement)("i",{className:"lp-icon-search"})))))},save:e=>null})})();