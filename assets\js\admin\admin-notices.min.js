import{lpOnElementReady}from"../utils.js";import API from"../api.js";let elLPAdminNotices=null,dataHtml=null;const queryString=window.location.search,urlParams=new URLSearchParams(queryString),tab=urlParams.get("tab"),notifyAddonsNewVersion=()=>{try{const e=document.querySelector("#adminmenu");if(!e)return;const t=e.querySelector("#toplevel_page_learn_press");if(!t)return;const n=t.querySelector(".wp-menu-name");if(!n)return;const s=document.querySelector("input[name=lp-addons-new-version-totals]");if(!s)return;const i='<span class="tab-lp-admin-notice"></span>';n.insertAdjacentHTML("beforeend",i);const a=t.querySelector('a[href="admin.php?page=learn-press-addons"]');if(!a)return;const o=`<span style="margin-left: 5px" class="update-plugins">${s.value}</span>`;a.setAttribute("href","admin.php?page=learn-press-addons&tab=update"),a.insertAdjacentHTML("beforeend",o)}catch(e){console.log(e)}},callAdminNotices=(e="")=>{if(!lpGlobalSettings.is_admin)return;let t=tab?`?tab=${tab}`:"";t+=e?(tab?"&":"?")+`${e}`:"",fetch(API.admin.apiAdminNotice+t,{method:"POST",headers:{"X-WP-Nonce":lpGlobalSettings.nonce}}).then((e=>e.json())).then((e=>{const{status:t,message:n,data:s}=e;"success"===t?"Dismissed!"!==n&&(dataHtml=s.content,0===dataHtml.length?elLPAdminNotices.style.display="none":(elLPAdminNotices.innerHTML=dataHtml,elLPAdminNotices.style.display="block",notifyAddonsNewVersion())):dataHtml=n})).catch((e=>{console.log(e)}))};lpOnElementReady(".lp-admin-notices",(()=>{elLPAdminNotices=document.querySelector(".lp-admin-notices"),callAdminNotices()})),document.addEventListener("click",(e=>{const t=e.target;if(t.classList.contains("btn-lp-notice-dismiss")&&(e.preventDefault(),confirm("Are you sure you want to dismiss this notice?"))){const e=t.closest(".lp-notice");callAdminNotices(`dismiss=${t.getAttribute("data-dismiss")}`),e.remove(),0===elLPAdminNotices.querySelectorAll(".lp-notice").length&&(elLPAdminNotices.style.display="none")}}));