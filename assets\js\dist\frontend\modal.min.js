(()=>{"use strict";var e={2747:e=>{function t(e,n){var r;if(Array.isArray(n))for(r=0;r<n.length;r++)t(e,n[r]);else for(r in n)e[r]=(e[r]||[]).concat(n[r])}e.exports=function(e){var n,r={};return t(r,e),n=function(e){return function(t){return function(n){var o,a,s=r[n.type],l=t(n);if(s)for(o=0;o<s.length;o++)(a=s[o](n,e))&&e.dispatch(a);return l}}},n.effects=r,n}}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,n),a.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};n.r(r),n.d(r,{default:()=>y});var o={};n.r(o),n.d(o,{confirm:()=>u,hide:()=>d,show:()=>i});var a={};n.r(a),n.d(a,{confirm:()=>f,getMessage:()=>m,isOpen:()=>p});const s=window.React,l=window.wp.data,c={};function i(e,t){return{type:"SHOW_MODAL",message:e,cb:t}}function d(){return{type:"HIDE_MODAL"}}function u(e){return{type:"CONFIRM",value:e}}function p(e){return e.isOpen}function m(e){return e.message}function f(e,t,n){const{show:r,hide:o}=(0,l.dispatch)("learnpress/modal");return e.message?(o(),e.confirm):(r(t,n),"no")}var w=n(2747),g=n.n(w);const h={ENROLL_COURSE_X:(e,t)=>{}},{controls:b}=LP.dataControls;!function(e){let t=()=>{throw new Error("Dispatching while constructing your middleware is not allowed. Other middleware would not be applied to this dispatch.")};const n={getState:e.getState,dispatch:(...e)=>t(...e)};t=g()(h)(n)(e.dispatch),e.dispatch=t}((0,l.registerStore)("learnpress/modal",{reducer:(e=c,t)=>{switch(t.type){case"SHOW_MODAL":return{...e,isOpen:!0,message:t.message,cb:t.cb};case"HIDE_MODAL":return{...e,isOpen:!1,message:!1,cb:null};case"CONFIRM":return e.cb&&setTimeout((()=>{e.cb()}),10),{...e,confirm:t.value}}return e},selectors:a,actions:o,controls:{...b}}));const v=window.wp.i18n,y=({children:e})=>{const{show:t,hide:n,confirm:r}=(0,l.dispatch)("learnpress/modal"),o=(0,l.useSelect)((e=>e("learnpress/modal").isOpen())),a=(0,l.useSelect)((e=>e("learnpress/modal").getMessage())),c=e=>t=>{r(e)},i={display:o?"block":"none"};return(0,s.createElement)(s.Fragment,null,(0,s.createElement)("div",null,(0,s.createElement)("div",{id:"lp-modal-overlay",style:i}),(0,s.createElement)("div",{id:"lp-modal-window",style:i},(0,s.createElement)("div",{id:"lp-modal-content",dangerouslySetInnerHTML:{__html:a}}),(0,s.createElement)("div",{id:"lp-modal-buttons"},(0,s.createElement)("button",{className:"lp-button modal-button-ok",onClick:c("yes")},(0,s.createElement)("span",null,(0,v.__)("OK","learnpress"))),(0,s.createElement)("button",{className:"lp-button modal-button-cancel",onClick:c("no")},(0,s.createElement)("span",null,(0,v.__)("Cancel","learnpress")))))),e)};(window.LP=window.LP||{}).modal=r})();