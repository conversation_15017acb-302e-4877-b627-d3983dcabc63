!function(a){var i,n=[],o=!1,f=!1,p={interval:250,force_process:!1},s=a(window);function c(){f=!1;for(var e=0;e<n.length;e++){var r,t=a(n[e]).filter(function(){return a(this).is(":appeared")});t.trigger("appear",[t]),i&&(r=i.not(t)).trigger("disappear",[r]),i=t}}a.expr[":"].appeared=function(e){var r=a(e);if(!r.is(":visible"))return!1;var t=s.scrollLeft(),i=s.scrollTop(),n=r.offset(),e=n.left,n=n.top;return n+r.height()>=i&&n-(r.data("appear-top-offset")||0)<=i+s.height()&&e+r.width()>=t&&e-(r.data("appear-left-offset")||0)<=t+s.width()},a.fn.extend({appear:function(e){var r=a.extend({},p,e||{}),t=this.selector||this;return o||(e=function(){f||(f=!0,setTimeout(c,r.interval))},a(window).scroll(e).resize(e),o=!0),r.force_process&&setTimeout(c,r.interval),n.push(t),a(t)}}),a.extend({force_appear:function(){return!!o&&(c(),!0)}})}(jQuery);