var isFunction;Object.prototype.watchChange||(isFunction=function(e){return e&&"[object Function]"==={}.toString.call(e)},Object.defineProperty(Object.prototype,"watchChange",{enumerable:!1,configurable:!0,writable:!1,value:function(e,t){var r=this;function n(t,n){var o=r[t],s=o;delete r[t]&&Object.defineProperty(r,t,{get:function(){return s},set:function(e){return s=n.call(r,t,o,e)},enumerable:!0,configurable:!0})}if(isFunction(e))for(var o in this)n(o,e);else n(e,t)}})),Object.prototype.unwatchChange||Object.defineProperty(Object.prototype,"unwatchChange",{enumerable:!1,configurable:!0,writable:!1,value:function(e){var t=this[e];delete this[e],this[e]=t}}),function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery"],e):"undefined"!=typeof module&&module.exports?module.exports=e(require("jquery")):e(jQuery)}(function(s){"use strict";var g=s.scrollTo=function(e,t,n){return s(window).scrollTo(e,t,n)};function r(e){return!e.nodeName||-1!==s.inArray(e.nodeName.toLowerCase(),["iframe","#document","html","body"])}function t(e){return s.isFunction(e)||s.isPlainObject(e)?e:{top:e,left:e}}return g.defaults={axis:"xy",duration:0,limit:!0},s.fn.scrollTo=function(e,n,_){"object"==typeof n&&(_=n,n=0),"function"==typeof _&&(_={onAfter:_}),"max"===e&&(e=9e9),_=s.extend({},g.defaults,_),n=n||_.duration;var p=_.queue&&1<_.axis.length;return p&&(n/=2),_.offset=t(_.offset),_.over=t(_.over),this.each(function(){if(null!==e){var a,c=r(this),u=c?this.contentWindow||window:this,l=s(u),d=e,f={};switch(typeof d){case"number":case"string":if(/^([+-]=?)?\d+(\.\d+)?(px|%)?$/.test(d)){d=t(d);break}d=c?s(d):s(d,u);case"object":if(0===d.length)return;(d.is||d.style)&&(a=(d=s(d)).offset())}var h=s.isFunction(_.offset)&&_.offset(u,d)||_.offset;s.each(_.axis.split(""),function(e,t){var n="x"===t?"Left":"Top",o=n.toLowerCase(),s="scroll"+n,r=l[s](),i=g.max(u,t);a?(f[s]=a[o]+(c?0:r-l.offset()[o]),_.margin&&(f[s]-=parseInt(d.css("margin"+n),10)||0,f[s]-=parseInt(d.css("border"+n+"Width"),10)||0),f[s]+=h[o]||0,_.over[o]&&(f[s]+=d["x"===t?"width":"height"]()*_.over[o])):(n=d[o],f[s]=n.slice&&"%"===n.slice(-1)?parseFloat(n)/100*i:n),_.limit&&/^\d+$/.test(f[s])&&(f[s]=f[s]<=0?0:Math.min(f[s],i)),!e&&1<_.axis.length&&(r===f[s]?f={}:p&&(m(_.onAfterFirst),f={}))}),m(_.onAfter)}function m(e){var t=s.extend({},_,{queue:!0,duration:n,complete:e&&function(){e.call(u,d,_)}});l.animate(f,t)}})},g.max=function(e,t){var t="x"===t?"Width":"Height",n="scroll"+t;if(!r(e))return e[n]-s(e)[t.toLowerCase()]();var t="client"+t,e=e.ownerDocument||e.document,o=e.documentElement,e=e.body;return Math.max(o[n],e[n])-Math.min(o[t],e[t])},s.Tween.propHooks.scrollLeft=s.Tween.propHooks.scrollTop={get:function(e){return s(e.elem)[e.prop]()},set:function(e){var t=this.get(e);if(e.options.interrupt&&e._last&&e._last!==t)return s(e.elem).stop();var n=Math.round(e.now);t!==n&&(s(e.elem)[e.prop](n),e._last=this.get(e))}},g}),function(o){o.backward_timer=function(e){var t={seconds:5,step:1,format:"h%:m%:s%",value_setter:void 0,on_exhausted:function(e){},on_tick:function(e){}},n=this;n.seconds_left=0,n.target=o(e),n.timeout=void 0,n.settings={},n.methods={init:function(e){n.settings=o.extend({},t,e),null==n.settings.value_setter&&(n.target.is("input")?n.settings.value_setter="val":n.settings.value_setter="text"),n.methods.reset()},start:function(){var e;null==n.timeout&&(e=n.seconds_left==n.settings.seconds?0:1e3*n.settings.step,setTimeout(n.methods._on_tick,e,e))},cancel:function(){null!=n.timeout&&(clearTimeout(n.timeout),n.timeout=void 0)},reset:function(){n.seconds_left=n.settings.seconds,n.methods._render_seconds()},_on_tick:function(e){0!=e&&n.settings.on_tick(n),n.methods._render_seconds(),0<n.seconds_left?(e=n.seconds_left<n.settings.step?n.seconds_left:n.settings.step,n.seconds_left-=e,e=1e3*e,n.timeout=setTimeout(n.methods._on_tick,e,e)):(n.timeout=void 0,n.settings.on_exhausted(n))},_render_seconds:function(){var e=n.methods._seconds_to_dhms(n.seconds_left),t=n.settings.format;t=(t=-1!==t.indexOf("d%")?t.replace("d%",e.d).replace("h%",n.methods._check_leading_zero(e.h)):t.replace("h%",24*e.d+e.h)).replace("m%",n.methods._check_leading_zero(e.m)).replace("s%",n.methods._check_leading_zero(e.s)),n.target[n.settings.value_setter](t)},_seconds_to_dhms:function(e){var t=Math.floor(e/86400),e=e-24*t*3600,n=Math.floor(e/3600),e=e-3600*n,o=Math.floor(e/60);return{d:t,h:n,m:o,s:Math.floor(e-60*o)}},_check_leading_zero:function(e){return e<10?"0"+e:""+e}}},o.fn.backward_timer=function(t){var n=arguments;return this.each(function(){var e=o(this).data("backward_timer");return null==e&&(e=new o.backward_timer(this),o(this).data("backward_timer",e)),e.methods[t]?e.methods[t].apply(this,Array.prototype.slice.call(n,1)):"object"!=typeof t&&t?void o.error("Method "+t+" does not exist on jQuery.backward_timer"):e.methods.init.apply(this,n)})}}(jQuery);