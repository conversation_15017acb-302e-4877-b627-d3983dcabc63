(()=>{"use strict";const e=window.React,t=window.wp.i18n,l=window.wp.blockEditor,n=window.wp.components,r=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/instructor-social","title":"Instructor Social","category":"learnpress-course-elements","icon":"format-image","description":"Renders template Instructor Social PHP templates.","textdomain":"learnpress","keywords":["instructor social single","learnpress"],"ancestor":["learnpress/single-instructor"],"attributes":{"target":{"type":"boolean","default":false},"nofollow":{"type":"boolean","default":false}},"usesContext":[],"supports":{"multiple":false,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}}}}'),s=window.wp.blocks,a=window.wp.primitives,o=window.ReactJSXRuntime,i=(0,o.jsx)(a.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,o.jsx)(a.Path,{d:"M12.5 14.5h-1V16h1c2.2 0 4-1.8 4-4s-1.8-4-4-4h-1v1.5h1c1.4 0 2.5 1.1 2.5 2.5s-1.1 2.5-2.5 2.5zm-4 1.5v-1.5h-1C6.1 14.5 5 13.4 5 12s1.1-2.5 2.5-2.5h1V8h-1c-2.2 0-4 1.8-4 4s1.8 4 4 4h1zm-1-3.2h5v-1.5h-5v1.5zM18 4H9c-1.1 0-2 .9-2 2v.5h1.5V6c0-.3.2-.5.5-.5h9c.3 0 .5.2.5.5v12c0 .3-.2.5-.5.5H9c-.3 0-.5-.2-.5-.5v-.5H7v.5c0 1.1.9 2 2 2h9c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2z"})});(0,s.registerBlockType)(r.name,{...r,icon:i,edit:r=>{const s=(0,l.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.InspectorControls,null,(0,e.createElement)(n.PanelBody,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(n.ToggleControl,{label:(0,t.__)("Open links in new tab","learnpress"),checked:!!r.attributes.target,onChange:e=>{r.setAttributes({target:!!e})}}),(0,e.createElement)(n.ToggleControl,{label:(0,t.__)("Add nofollow attribute","learnpress"),checked:!!r.attributes.nofollow,onChange:e=>{r.setAttributes({nofollow:!!e})}}))),(0,e.createElement)("div",{...s},(0,e.createElement)("div",{className:"instructor-social"},(0,e.createElement)("i",{className:"lp-user-ico lp-icon-facebook"}),(0,e.createElement)("i",{className:"lp-user-ico lp-icon-twitter"}),(0,e.createElement)("i",{className:"lp-user-ico lp-icon-youtube-play"}),(0,e.createElement)("i",{className:"lp-user-ico lp-icon-linkedin"}))))},save:e=>null})})();