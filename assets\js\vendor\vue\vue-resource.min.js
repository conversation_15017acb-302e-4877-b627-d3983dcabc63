!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.VueResource=e()}(this,function(){"use strict";function s(t){this.state=2,this.value=void 0,this.deferred=[];var e=this;try{t(function(t){e.resolve(t)},function(t){e.reject(t)})}catch(t){e.reject(t)}}s.reject=function(n){return new s(function(t,e){e(n)})},s.resolve=function(n){return new s(function(t,e){t(n)})},s.all=function(i){return new s(function(n,t){var o=0,r=[];0===i.length&&n(r);for(var e=0;e<i.length;e+=1)s.resolve(i[e]).then(function(e){return function(t){r[e]=t,(o+=1)===i.length&&n(r)}}(e),t)})},s.race=function(o){return new s(function(t,e){for(var n=0;n<o.length;n+=1)s.resolve(o[n]).then(t,e)})};var t=s.prototype;function f(t,e){t instanceof Promise?this.promise=t:this.promise=new Promise(t.bind(e)),this.context=e}t.resolve=function(t){var e=this;if(2===e.state){if(t===e)throw new TypeError("Promise settled with itself.");var n=!1;try{var o=t&&t.then;if(null!==t&&"object"==typeof t&&"function"==typeof o)return void o.call(t,function(t){n||e.resolve(t),n=!0},function(t){n||e.reject(t),n=!0})}catch(t){return void(n||e.reject(t))}e.state=0,e.value=t,e.notify()}},t.reject=function(t){var e=this;if(2===e.state){if(t===e)throw new TypeError("Promise settled with itself.");e.state=1,e.value=t,e.notify()}},t.notify=function(){var i=this;n(function(){if(2!==i.state)for(;i.deferred.length;){var t=i.deferred.shift(),e=t[0],n=t[1],o=t[2],r=t[3];try{0===i.state?o("function"==typeof e?e.call(void 0,i.value):i.value):1===i.state&&("function"==typeof n?o(n.call(void 0,i.value)):r(i.value))}catch(t){r(t)}}},void 0)},t.then=function(n,o){var r=this;return new s(function(t,e){r.deferred.push([n,o,t,e]),r.notify()})},t.catch=function(t){return this.then(void 0,t)},"undefined"==typeof Promise&&(window.Promise=s),f.all=function(t,e){return new f(Promise.all(t),e)},f.resolve=function(t,e){return new f(Promise.resolve(t),e)},f.reject=function(t,e){return new f(Promise.reject(t),e)},f.race=function(t,e){return new f(Promise.race(t),e)};t=f.prototype;t.bind=function(t){return this.context=t,this},t.then=function(t,e){return t&&t.bind&&this.context&&(t=t.bind(this.context)),e&&e.bind&&this.context&&(e=e.bind(this.context)),new f(this.promise.then(t,e),this.context)},t.catch=function(t){return t&&t.bind&&this.context&&(t=t.bind(this.context)),new f(this.promise.catch(t),this.context)},t.finally=function(e){return this.then(function(t){return e.call(this),t},function(t){return e.call(this),Promise.reject(t)})};var n,r={}.hasOwnProperty,o=[].slice,p=!1,i="undefined"!=typeof window,e=function(t){var e=t.config,t=t.nextTick;n=t,p=e.debug||!e.silent};function a(t){return t?t.replace(/^\s*|\s*$/g,""):""}function u(t){return t?t.toLowerCase():""}var c=Array.isArray;function h(t){return"string"==typeof t}function d(t){return"function"==typeof t}function l(t){return null!==t&&"object"==typeof t}function m(t){return l(t)&&Object.getPrototypeOf(t)==Object.prototype}function y(t,e,n){t=f.resolve(t);return arguments.length<2?t:t.then(e,n)}function v(t,e,n){return d(n=n||{})&&(n=n.call(e)),w(t.bind({$vm:e,$options:n}),t,{$options:n})}function b(t,e){var n,o;if(c(t))for(n=0;n<t.length;n++)e.call(t[n],t[n],n);else if(l(t))for(o in t)r.call(t,o)&&e.call(t[o],t[o],o);return t}var g=Object.assign||function(e){return o.call(arguments,1).forEach(function(t){T(e,t)}),e};function w(e){return o.call(arguments,1).forEach(function(t){T(e,t,!0)}),e}function T(t,e,n){for(var o in e)n&&(m(e[o])||c(e[o]))?(m(e[o])&&!m(t[o])&&(t[o]={}),c(e[o])&&!c(t[o])&&(t[o]=[]),T(t[o],e[o],n)):void 0!==e[o]&&(t[o]=e[o])}function x(t,e,n){var o,s,u,t=(o=t,s=["+","#",".","/",";","?","&"],{vars:u=[],expand:function(i){return o.replace(/\{([^\{\}]+)\}|([^\{\}]+)/g,function(t,e,n){if(e){var o=null,r=[];if(-1!==s.indexOf(e.charAt(0))&&(o=e.charAt(0),e=e.substr(1)),e.split(/,/g).forEach(function(t){t=/([^:\*]*)(?::(\d+)|(\*))?/.exec(t);r.push.apply(r,function(t,e,n,o){var r=t[n],i=[];{var s;j(r)&&""!==r?"string"==typeof r||"number"==typeof r||"boolean"==typeof r?(r=r.toString(),o&&"*"!==o&&(r=r.substring(0,parseInt(o,10))),i.push(O(e,r,E(e)?n:null))):"*"===o?Array.isArray(r)?r.filter(j).forEach(function(t){i.push(O(e,t,E(e)?n:null))}):Object.keys(r).forEach(function(t){j(r[t])&&i.push(O(e,r[t],t))}):(s=[],Array.isArray(r)?r.filter(j).forEach(function(t){s.push(O(e,t))}):Object.keys(r).forEach(function(t){j(r[t])&&(s.push(encodeURIComponent(t)),s.push(O(e,r[t].toString())))}),E(e)?i.push(encodeURIComponent(n)+"="+s.join(",")):0!==s.length&&i.push(s.join(","))):";"===e?i.push(encodeURIComponent(n)):""!==r||"&"!==e&&"?"!==e?""===r&&i.push(""):i.push(encodeURIComponent(n)+"=")}return i}(i,o,t[1],t[2]||t[3])),u.push(t[1])}),o&&"+"!==o){e=",";return"?"===o?e="&":"#"!==o&&(e=o),(0!==r.length?o:"")+r.join(e)}return r.join(",")}return P(n)})}}),e=t.expand(e);return n&&n.push.apply(n,t.vars),e}function j(t){return null!=t}function E(t){return";"===t||"&"===t||"?"===t}function O(t,e,n){return e=("+"===t||"#"===t?P:encodeURIComponent)(e),n?encodeURIComponent(n)+"="+e:e}function P(t){return t.split(/(%[0-9A-Fa-f]{2})/g).map(function(t){return/%[0-9A-Fa-f]/.test(t)||(t=encodeURI(t)),t}).join("")}function C(t,e){var r,i=this||{},n=t;return h(t)&&(n={url:t,params:e}),n=w({},C.options,i.$options,n),C.transforms.forEach(function(t){var e,n,o;h(t)&&(t=C.transform[t]),d(t)&&(e=t,n=r,o=i.$vm,r=function(t){return e.call(o,t,n)})}),r(n)}C.options={url:"",root:null,params:{}},C.transform={template:function(e){var t=[],n=x(e.url,e.params,t);return t.forEach(function(t){delete e.params[t]}),n},query:function(t,e){var n=Object.keys(C.options.params),o={},e=e(t);return b(t.params,function(t,e){-1===n.indexOf(e)&&(o[e]=t)}),(o=C.params(o))&&(e+=(-1==e.indexOf("?")?"?":"&")+o),e},root:function(t,e){var n=e(t);return h(t.root)&&!/^(https?:)?\//.test(n)&&(e=t.root,t="/",n=(e&&void 0===t?e.replace(/\s+$/,""):e&&t?e.replace(new RegExp("["+t+"]+$"),""):e)+"/"+n),n}},C.transforms=["template","query","root"],C.params=function(t){var e=[],n=encodeURIComponent;return e.add=function(t,e){d(e)&&(e=e()),null===e&&(e=""),this.push(n(t)+"="+n(e))},function n(o,t,r){var i,s=c(t),u=m(t);b(t,function(t,e){i=l(t)||c(t),r&&(e=r+"["+(u||i?e:"")+"]"),!r&&s?o.add(t.name,t.value):i?n(o,t,e):o.add(e,t)})}(e,t),e.join("&").replace(/%20/g,"+")},C.parse=function(t){var e=document.createElement("a");return document.documentMode&&(e.href=t,t=e.href),e.href=t,{href:e.href,protocol:e.protocol?e.protocol.replace(/:$/,""):"",port:e.port,host:e.host,hostname:e.hostname,pathname:"/"===e.pathname.charAt(0)?e.pathname:"/"+e.pathname,search:e.search?e.search.replace(/^\?/,""):"",hash:e.hash?e.hash.replace(/^#/,""):""}};function $(r){return new f(function(n){function t(t){var e=t.type,t=0;"load"===e?t=200:"error"===e&&(t=500),n(r.respondWith(o.responseText,{status:t}))}var o=new XDomainRequest;r.abort=function(){return o.abort()},o.open(r.method,r.getUrl()),r.timeout&&(o.timeout=r.timeout),o.onload=t,o.onabort=t,o.onerror=t,o.ontimeout=t,o.onprogress=function(){},o.send(r.getBody())})}var U=i&&"withCredentials"in new XMLHttpRequest;function R(s){return new f(function(n){var o,t=s.jsonp||"callback",r=s.jsonpCallback||"_jsonp"+Math.random().toString(36).substr(2),i=null,e=function(t){var e=t.type,t=0;"load"===e&&null!==i?t=200:"error"===e&&(t=500),t&&window[r]&&(delete window[r],document.body.removeChild(o)),n(s.respondWith(i,{status:t}))};window[r]=function(t){i=JSON.stringify(t)},s.abort=function(){e({type:"abort"})},s.params[t]=r,s.timeout&&setTimeout(s.abort,s.timeout),(o=document.createElement("script")).src=s.getUrl(),o.type="text/javascript",o.async=!0,o.onload=e,o.onerror=e,document.body.appendChild(o)})}function A(r){return new f(function(n){function t(t){var e=r.respondWith("response"in o?o.response:o.responseText,{status:1223===o.status?204:o.status,statusText:1223===o.status?"No Content":a(o.statusText)});b(a(o.getAllResponseHeaders()).split("\n"),function(t){e.headers.append(t.slice(0,t.indexOf(":")),t.slice(t.indexOf(":")+1))}),n(e)}var o=new XMLHttpRequest;r.abort=function(){return o.abort()},r.progress&&("GET"===r.method?o.addEventListener("progress",r.progress):/^(POST|PUT)$/i.test(r.method)&&o.upload.addEventListener("progress",r.progress)),o.open(r.method,r.getUrl(),!0),r.timeout&&(o.timeout=r.timeout),r.responseType&&"responseType"in o&&(o.responseType=r.responseType),(r.withCredentials||r.credentials)&&(o.withCredentials=!0),r.crossOrigin||r.headers.set("X-Requested-With","XMLHttpRequest"),r.headers.forEach(function(t,e){o.setRequestHeader(e,t)}),o.onload=t,o.onabort=t,o.onerror=t,o.ontimeout=t,o.send(r.getBody())})}function S(s){var u=require("got");return new f(function(e){var n,t=s.getUrl(),o=s.getBody(),r=s.method,i={};s.headers.forEach(function(t,e){i[e]=t}),u(t,{body:o,method:r,headers:i}).then(n=function(t){var n=s.respondWith(t.body,{status:t.statusCode,statusText:a(t.statusMessage)});b(t.headers,function(t,e){n.headers.set(e,t)}),e(n)},function(t){return n(t.response)})})}var k=function(s){var u,a=[I],c=[];function t(i){return new f(function(e,n){function o(){var t;d(u=a.pop())?u.call(s,i,r):(t="Invalid interceptor of type "+typeof u+", must be a function","undefined"!=typeof console&&p&&console.warn("[VueResource warn]: "+t),r())}function r(t){if(d(t))c.unshift(t);else if(l(t))return c.forEach(function(e){t=y(t,function(t){return e.call(s,t)||t},n)}),void y(t,e,n);o()}o()},s)}return l(s)||(s=null),t.use=function(t){a.push(t)},t};function I(t,e){e((t.client||(i?A:S))(t))}function q(t){var n=this;this.map={},b(t,function(t,e){return n.append(e,t)})}function H(t,n){return Object.keys(t).reduce(function(t,e){return u(n)===u(e)?e:t},null)}q.prototype.has=function(t){return null!==H(this.map,t)},q.prototype.get=function(t){t=this.map[H(this.map,t)];return t?t.join():null},q.prototype.getAll=function(t){return this.map[H(this.map,t)]||[]},q.prototype.set=function(t,e){this.map[function(t){if(/[^a-z0-9\-#$%&'*+.\^_`|~]/i.test(t))throw new TypeError("Invalid character in header field name");return a(t)}(H(this.map,t)||t)]=[a(e)]},q.prototype.append=function(t,e){var n=this.map[H(this.map,t)];n?n.push(a(e)):this.set(t,e)},q.prototype.delete=function(t){delete this.map[H(this.map,t)]},q.prototype.deleteAll=function(){this.map={}},q.prototype.forEach=function(n,o){var r=this;b(this.map,function(t,e){b(t,function(t){return n.call(o,t,e,r)})})};function L(t,e){var n,o=e.url,r=e.headers,i=e.status,e=e.statusText;this.url=o,this.ok=200<=i&&i<300,this.status=i||0,this.statusText=e||"",this.headers=new q(r),h(this.body=t)?this.bodyText=t:(r=t,"undefined"!=typeof Blob&&r instanceof Blob&&(this.bodyBlob=t,0!==(r=t).type.indexOf("text")&&-1===r.type.indexOf("json")||(this.bodyText=(n=t,new f(function(t){var e=new FileReader;e.readAsText(n),e.onload=function(){t(e.result)}})))))}L.prototype.blob=function(){return y(this.bodyBlob)},L.prototype.text=function(){return y(this.bodyText)},L.prototype.json=function(){return y(this.text(),function(t){return JSON.parse(t)})},Object.defineProperty(L.prototype,"data",{get:function(){return this.body},set:function(t){this.body=t}});var B=function(t){this.body=null,this.params={},g(this,t,{method:(t=t.method||"GET")?t.toUpperCase():""}),this.headers instanceof q||(this.headers=new q(this.headers))};B.prototype.getUrl=function(){return C(this)},B.prototype.getBody=function(){return this.body},B.prototype.respondWith=function(t,e){return new L(t,g(e||{},{url:this.getUrl()}))};t={"Content-Type":"application/json;charset=utf-8"};function M(t){var e=this||{},n=k(e.$vm);return function(n){o.call(arguments,1).forEach(function(t){for(var e in t)void 0===n[e]&&(n[e]=t[e])})}(t||{},e.$options,M.options),M.interceptors.forEach(function(t){h(t)&&(t=M.interceptor[t]),d(t)&&n.use(t)}),n(new B(t)).then(function(t){return t.ok?t:f.reject(t)},function(t){var e;return t instanceof Error&&(e=t,"undefined"!=typeof console&&console.error(e)),f.reject(t)})}function N(n,o,t,r){var i=this||{},s={};return b(t=g({},N.actions,t),function(t,e){t=w({url:n,params:g({},o)},r,t),s[e]=function(){return(i.$http||M)(function(t,e){var n,o=g({},t),t={};switch(e.length){case 2:t=e[0],n=e[1];break;case 1:/^(POST|PUT|PATCH)$/i.test(o.method)?n=e[0]:t=e[0];break;case 0:break;default:throw"Expected up to 2 arguments [params, body], got "+e.length+" arguments"}return o.body=n,o.params=g({},o.params,t),o}(t,arguments))}}),s}function D(n){D.installed||(e(n),n.url=C,n.http=M,n.resource=N,n.Promise=f,Object.defineProperties(n.prototype,{$url:{get:function(){return v(n.url,this,this.$options.url)}},$http:{get:function(){return v(n.http,this,this.$options.http)}},$resource:{get:function(){return n.resource.bind(this)}},$promise:{get:function(){var e=this;return function(t){return new n.Promise(t,e)}}}}))}return M.options={},M.headers={put:t,post:t,patch:t,delete:t,common:{Accept:"application/json, text/plain, */*"},custom:{}},M.interceptor={before:function(t,e){d(t.before)&&t.before.call(this,t),e()},method:function(t,e){t.emulateHTTP&&/^(PUT|PATCH|DELETE)$/i.test(t.method)&&(t.headers.set("X-HTTP-Method-Override",t.method),t.method="POST"),e()},jsonp:function(t,e){"JSONP"==t.method&&(t.client=R),e()},json:function(t,e){var r=t.headers.get("Content-Type")||"";l(t.body)&&0===r.indexOf("application/json")&&(t.body=JSON.stringify(t.body)),e(function(o){return o.bodyText?y(o.text(),function(t){if(0===(r=o.headers.get("Content-Type")||"").indexOf("application/json")||(n=(e=t).match(/^\[|^\{(?!\{)/))&&{"[":/]$/,"{":/}$/}[n[0]].test(e))try{o.body=JSON.parse(t)}catch(t){o.body=null}else o.body=t;var e,n;return o}):o})},form:function(t,e){var n;n=t.body,"undefined"!=typeof FormData&&n instanceof FormData?t.headers.delete("Content-Type"):l(t.body)&&t.emulateJSON&&(t.body=C.params(t.body),t.headers.set("Content-Type","application/x-www-form-urlencoded")),e()},header:function(n,t){b(g({},M.headers.common,n.crossOrigin?{}:M.headers.custom,M.headers[u(n.method)]),function(t,e){n.headers.has(e)||n.headers.set(e,t)}),t()},cors:function(t,e){var n,o;i&&(n=C.parse(location.href),(o=C.parse(t.getUrl())).protocol===n.protocol&&o.host===n.host||(t.crossOrigin=!0,t.emulateHTTP=!1,U||(t.client=$))),e()}},M.interceptors=["before","method","jsonp","json","form","header","cors"],["get","delete","head","jsonp"].forEach(function(n){M[n]=function(t,e){return this(g(e||{},{url:t,method:n}))}}),["post","put","patch"].forEach(function(o){M[o]=function(t,e,n){return this(g(n||{},{url:t,method:o,body:e}))}}),N.actions={get:{method:"GET"},save:{method:"POST"},query:{method:"GET"},update:{method:"PUT"},remove:{method:"DELETE"},delete:{method:"DELETE"}},"undefined"!=typeof window&&window.Vue&&window.Vue.use(D),D});