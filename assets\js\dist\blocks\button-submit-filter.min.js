(()=>{"use strict";const e=window.React,t=window.wp.i18n,r=window.wp.blockEditor,a=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/button-submit-filter","title":"Button Submit Filter","category":"learnpress-course-elements","description":"Renders template Button Submit Filter PHP templates.","textdomain":"learnpress","keywords":["category button submit filter course","learnpress"],"usesContext":[],"supports":{"multiple":false,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":true,"text":true,"__experimentalDefaultControls":{"background":true,"text":true}},"__experimentalBorder":{"color":true,"radius":true,"width":true,"__experimentalDefaultControls":{"width":false,"color":false,"radius":false}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"padding":false,"margin":false}}},"ancestor":["learnpress/course-filter"]}');(0,window.wp.blocks.registerBlockType)("learnpress/button-submit-filter",{...a,icon:{src:(0,e.createElement)("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",className:"wc-block-editor-components-block-icon","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{d:"M8 12.5h8V11H8v1.5Z M19 6.5H5a2 2 0 0 0-2 2V15a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8.5a2 2 0 0 0-2-2ZM5 8h14a.5.5 0 0 1 .5.5V15a.5.5 0 0 1-.5.5H5a.5.5 0 0 1-.5-.5V8.5A.5.5 0 0 1 5 8Z"}))},edit:a=>{const l=(0,r.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...l},(0,t.__)("Apply","learnpress")))},save:e=>null})})();