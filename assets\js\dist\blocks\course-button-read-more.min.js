(()=>{"use strict";const e=window.React,t=window.wp.i18n,r=window.wp.blockEditor,o=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-button-read-more","title":"Course Button Read More","category":"learnpress-course-elements","icon":"button","description":"Renders template Button Course Read More PHP templates.","textdomain":"learnpress","keywords":["button read more single course","learnpress"],"ancestor":["learnpress/course-item-template"],"usesContext":["lpCourseData"],"supports":{"multiple":true,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":true,"text":true,"__experimentalDefaultControls":{"background":true,"text":true}},"__experimentalBorder":{"color":true,"radius":true,"width":true,"__experimentalDefaultControls":{"width":false,"color":false,"radius":false}},"spacing":{"margin":true,"padding":true,"content":true,"__experimentalDefaultControls":{"margin":false,"padding":false,"content":true}}}}'),s=window.wp.blocks;window.wp.data,(0,s.registerBlockType)(o.name,{...o,edit:o=>{const s=(0,r.useBlockProps)(),{attributes:a,setAttributes:n,context:l}=o,{lpCourseData:u}=l,i=(0,t.__)("Read more","learnpress");let p=s.className;return p=p.replaceAll("wp-block-learnpress-course-button-read-more",""),(0,e.createElement)(e.Fragment,null,(0,e.createElement)("a",{...s,dangerouslySetInnerHTML:{__html:i}}))},save:e=>null})})();