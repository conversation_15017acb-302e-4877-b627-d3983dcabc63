(()=>{"use strict";jQuery((function(e){e('form[name="become-teacher-form"]').each((function(){const t=e(this),r=t.find('button[type="submit"]'),a=function(r){let a=[];if(e.isPlainObject(r))for(const t in r)a.push(e(r[t]));else a=e.isArray(r)?r.reverse():[r];for(let r=0;r<a.length;r++)e(a[r]).insertBefore(t)},n=function(e){return t.find("input, select, button, textarea").prop("disabled",!!e)},s=function(){e(".learn-press-error, .learn-press-message").fadeOut("fast",(function(){e(this).remove()})),n(!0).filter(r).data("origin-text",r.text()).html(r.data("text"))},i=function(e){(e=(e=>{if("string"!=typeof e)return e;const t=String.raw({raw:e}).match(/<-- LP_AJAX_START -->(.*)<-- LP_AJAX_END -->/s);try{e=t?JSON.parse(t[1].replace(/(?:\r\n|\r|\n)/g,"")):JSON.parse(e)}catch(t){e={}}return e})(e)).message&&a(e.message),n().filter(r).html(r.data("origin-text")),"success"===e.result?t.remove():(r.prop("disabled",!1),r.html(r.data("text")))},o=function(e){(e=LP.parseJSON(e)).message&&a(e.message),n().filter(r).html(r.data("origin-text"))};t.on("submit",(function(r){if(r.preventDefault(),!1!==t.triggerHandler("become_teacher_send")){const r=new URL(window.location.href);r.searchParams.set("lp-ajax","request-become-a-teacher"),e.ajax({url:r,data:t.serialize(),dataType:"text",type:"POST",beforeSend:s,success:i,error:o})}return!1}))}))}))})();