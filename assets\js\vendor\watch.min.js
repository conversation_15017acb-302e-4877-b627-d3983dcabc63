var isFunction;Object.prototype.watchChange||(isFunction=function(e){return e&&"[object Function]"==={}.toString.call(e)},Object.defineProperty(Object.prototype,"watchChange",{enumerable:!1,configurable:!0,writable:!1,value:function(e,t){var c=this;function n(t,n){var r=c[t],i=r;delete c[t]&&Object.defineProperty(c,t,{get:function(){return i},set:function(e){return i=n.call(c,t,r,e)},enumerable:!0,configurable:!0})}if(isFunction(e))for(var r in this)n(r,e);else n(e,t)}})),Object.prototype.unwatchChange||Object.defineProperty(Object.prototype,"unwatchChange",{enumerable:!1,configurable:!0,writable:!1,value:function(e){var t=this[e];delete this[e],this[e]=t}});