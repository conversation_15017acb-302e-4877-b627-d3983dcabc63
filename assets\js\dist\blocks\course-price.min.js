(()=>{"use strict";const e=window.React,t=(window.wp.i18n,window.wp.blockEditor),r=window.wp.element,s=s=>{const{attributes:n,setAttributes:a,context:l}=s,o=(0,t.useBlockProps)(),{lpCourseData:i}=l,p=i?.price||'<span class="course-price"><span class="course-item-price"> <span class="origin-price">$5.00</span><span class="price">$4.00</span> </span></span>';return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...o},(0,e.createElement)(r.RawHTML,null,p)))},n=e=>null,a=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-price","title":"Course Price","category":"learnpress-course-elements","icon":"money-alt","description":"Renders template Price Course PHP templates.","textdomain":"learnpress","keywords":["price single course","learnpress"],"ancestor":["learnpress/single-course","learnpress/course-item-template"],"usesContext":["lpCourseData"],"supports":{"multiple":true,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":false}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),l=window.wp.blocks,o=window.wp.data;let i=null;var p,c,u;p=["learnpress/learnpress//single-lp_course"],c=a,u=e=>{(0,l.registerBlockType)(e.name,{...e,edit:s,save:n})},(0,o.subscribe)((()=>{const e={...c},t=(0,o.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&i!==r&&(i=r,(0,l.getBlockType)(e.name)&&((0,l.unregisterBlockType)(e.name),p.includes(r)?(e.ancestor=null,u(e)):(e.ancestor||(e.ancestor=[]),u(e))))})),(0,l.registerBlockType)(a.name,{...a,edit:s,save:n})})();