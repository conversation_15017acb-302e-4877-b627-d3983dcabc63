!function(e){function t(t,i){this.options=e.extend({ID:"",name:"Add new page"},i||{});const n=e(t),a=n.find("select"),s=n.find(".list-pages-wrapper"),o=n.find(".quick-add-page-actions"),d=n.find(".quick-add-page-inline");a.on("change",(function(){o.addClass("hide-if-js"),"add_new_page"===this.value?(s.addClass("hide-if-js"),d.removeClass("hide-if-js").find("input").trigger("focus").val("")):parseInt(this.value)&&(o.find("a.edit-page").attr("href","post.php?post="+this.value+"&action=edit"),o.find("a.view-page").attr("href",lpGlobalSettings.siteurl+"?page_id="+this.value),o.removeClass("hide-if-js"),a.attr("data-selected",this.value))})),n.on("click",".quick-add-page-inline button",(function(){const t=e(this),i=d.find("input"),n=i.val();if(!n)return alert("Please enter the name of page"),void i.trigger("focus");t.prop("disabled",!0);const a=t.closest("tr");let o="";a.length&&(o=a.find(".field_name").attr("name")),e.ajax({url:lpGlobalSettings.ajax,data:{action:"learnpress_create_page",page_name:n,field_name:o,nonce:lpDataAdmin.nonce},type:"post",dataType:"html",success(i){(i=LP.parseJSON(i)).page?(!function(t){const i=e('<option value="'+t.ID+'">'+t.name+"</option>"),n=e.inArray(t.ID+"",t.positions);e(".learn-press-dropdown-pages select").each((function(){const a=e(this),s=i.clone();0===n?e("option",a).each((function(){if(parseInt(e(this).val()))return s.insertBefore(e(this)),!1})):n===t.positions.length-1?a.append(s):s.insertAfter(e('option[value="'+t.positions[n-1]+'"]',a))}))}({ID:i.page.ID,name:i.page.post_title,positions:i.positions}),window.location.reload(),d.addClass("hide-if-js")):i.error&&alert(i.error),t.prop("disabled",!1),s.removeClass("hide-if-js")}})})).on("click",".quick-add-page-inline a",(function(e){e.preventDefault(),d.addClass("hide-if-js"),a.val(a.attr("data-selected")+"").removeAttr("disabled").trigger("change"),s.removeClass("hide-if-js")})).on("click",".button-quick-add-page",(function(e){a.val("add_new_page").trigger("change")})).on("keypress keydown",'.quick-add-page-inline input[type="text"]',(function(t){"Enter"==t.key&&"keypress"==t.type?(t.preventDefault(),e(this).siblings("button").trigger("click")):"Escape"==t.key&&"keydown"==t.type&&e(this).siblings("a").trigger("click")}))}e.fn.LP("DropdownPages",(function(){return e.each(this,(function(){let i=e(this).data("DropdownPages");return i||(i=new t(this,{}),e(this).data("DropdownPages",i)),i}))}))}(jQuery);