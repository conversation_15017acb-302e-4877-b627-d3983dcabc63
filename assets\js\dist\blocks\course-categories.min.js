(()=>{"use strict";const e=window.React,t=window.wp.i18n,r=window.wp.blockEditor,n=window.wp.components,l=l=>{const s=(0,r.useBlockProps)(),{attributes:a,setAttributes:o,context:i}=l,{lpCourseData:c}=i,u=c?.category||"Category";return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(r.<PERSON>,null,(0,e.createElement)(n.PanelBody,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(n.<PERSON>ggle<PERSON>ontrol,{label:(0,t.__)("Show text 'by'","learnpress"),checked:a.showText,onChange:e=>{o({showText:e})}}),(0,e.createElement)(n.ToggleControl,{label:(0,t.__)("Make the category a link","learnpress"),checked:a.isLink,onChange:e=>{o({isLink:e})}}),a.isLink?(0,e.createElement)(n.<PERSON>ggleControl,{label:(0,t.__)("Open is new tab","learnpress"),checked:a.target,onChange:e=>{o({target:e})}}):"")),(0,e.createElement)("div",{...s},(0,e.createElement)("div",{className:"is-layout-flex c-gap-4"},a.showText?"in ":"",(0,e.createElement)("div",{className:"course-categories"},(0,e.createElement)("div",{dangerouslySetInnerHTML:{__html:u}})))))},s=e=>null,a=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-categories","title":"Course Category","category":"learnpress-course-elements","icon":"category","description":"Renders template List Category Course PHP templates.","textdomain":"learnpress","keywords":["category Category single course","learnpress"],"ancestor":["learnpress/single-course","learnpress/course-item-template"],"usesContext":[],"attributes":{"showText":{"type":"boolean","default":true},"isLink":{"type":"boolean","default":true},"target":{"type":"boolean","default":false}},"supports":{"multiple":true,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":true,"__experimentalDefaultControls":{"link":false,"text":true}}}}'),o=window.wp.blocks,i=window.wp.data;let c=null;var u,p,g;u=["learnpress/learnpress//single-lp_course"],p=a,g=e=>{(0,o.registerBlockType)(e.name,{...e,edit:l,save:s})},(0,i.subscribe)((()=>{const e={...p},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&c!==r&&(c=r,(0,o.getBlockType)(e.name)&&((0,o.unregisterBlockType)(e.name),u.includes(r)?(e.ancestor=null,g(e)):(e.ancestor||(e.ancestor=[]),g(e))))})),(0,o.registerBlockType)(a.name,{...a,edit:l,save:s})})();