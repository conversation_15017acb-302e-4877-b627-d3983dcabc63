import{AdminUtilsFunctions,Api,Utils}from"../utils-admin.js";const addCoursesToOrder=()=>{let e,t,r,s,o,n,a,l,i,c;const d="#modal-search-items";let u={search:"",id_not_in:"",paged:1};const p=[],m=[],f=(t="",s=[],o=1)=>{let n="";s.length>0&&(n=s.join(",")),u={search:t,id_not_in:n,paged:o},AdminUtilsFunctions.fetchCourses(t,u,{before(){e.classList.add("loading")},success(t){const{data:s,status:n,message:a}=t,{courses:l,total_pages:i}=s;if("success"!==n)console.error(a);else{if(!l.length)return void(r.innerHTML='<li class="lp-result-item">No courses found</li>');r.innerHTML=h(l);const t=y(o,i);e.querySelector(".search-nav").innerHTML=t}},error(e){console.error(e)},completed(){e.classList.remove("loading")}})},h=e=>{let t="";return e.forEach((e=>{const r=parseInt(e.ID),s=p.includes(r)?"checked":"";t+=`\n\t\t\t<li class="lp-result-item" data-id="${r}" data-type="lp_course" data-text="${e.post_title}">\n\t\t\t\t<label>\n\t\t\t\t\t<input type="checkbox" value="${r}" name="selectedItems[]" ${s}>\n\t\t\t\t\t<span class="lp-item-text">${e.post_title} (#${r})</span>\n\t\t\t\t</label>\n\t\t\t</li>`})),t},y=(e,t)=>{e=parseInt(e);let r="";if((t=parseInt(t))<=1)return r;const s=e+1,o=e-1;let n=[];if(t<=9)for(let e=1;e<=t;e++)n.push(e);else if(e<=3)n=[1,2,3,4,5,"x",t];else if(e<=5){for(let t=1;t<=e;t++)n.push(t);for(let t=1;t<=2;t++){const r=e+t;n.push(r)}n.push("x"),n.push(t)}else{n=[1,"x"];for(let t=2;t>=0;t--){const r=e-t;n.push(r)}if(t-e<=5)for(let r=e+1;r<=t;r++)n.push(r);else{for(let t=1;t<=2;t++){const r=e+t;n.push(r)}n.push("x"),n.push(t)}}const a=n.length;1!==e&&(r+=`<a class="prev page-numbers button" href="#" data-page="${o}"><</a>`);for(let t=0;t<a;t++)e===parseInt(n[t])?r+=`<a aria-current="page" class="page-numbers current button disabled" data-page="${n[t]}">\n\t\t\t\t${n[t]}\n\t\t\t</a>`:"x"===n[t]?r+='<span class="page-numbers dots button disabled">...</span>':r+=`<a class="page-numbers button" href="#" data-page="${n[t]}">${n[t]} </a>`;return e!==t&&(r+=`<a class="next page-numbers button" href="#" data-page="${s}">></a>`),r};document.addEventListener("click",(o=>{const a=o.target;if(t&&a.id===t.id&&(o.preventDefault(),n.style.display="block",l.style.display="none",r.innerHTML="",f(u.search,m,u.paged)),a.classList.contains("close")&&a.closest(d)&&(o.preventDefault(),e.querySelector('input[name="search"]').value="",u.search="",u.paged=1,n.style.display="none"),a.classList.contains("page-numbers")&&a.closest(d)){o.preventDefault();const e=a.getAttribute("data-page");f(u.search,u.id_not_in,e)}if("selectedItems[]"===a.name&&a.closest(d)){const e=parseInt(a.value);if(a.checked)p.push(e);else{const t=p.indexOf(e);t>-1&&p.splice(t,1)}l.style.display=p.length>0?"block":"none"}((e,t)=>{if(!t.classList.contains("add"))return;if(!t.closest(d))return;e.preventDefault(),t.disabled=!0;const r={"lp-ajax":"add_items_to_order",order_id:document.querySelector("#post_ID").value,items:p,nonce:lpDataAdmin.nonce},o={success(e){const{data:t,messages:r,status:o}=e;if("error"===o)return void console.error(r);const{item_html:n,order_data:a}=t,l=i.querySelector(".no-order-items");l.style.display="none",l.insertAdjacentHTML("beforebegin",n),s.querySelector(".order-subtotal").innerHTML=a.subtotal_html,s.querySelector(".order-total").innerHTML=a.total_html,m.push(...p),p.splice(0,p.length)},error(e){console.error(e)},completed(){t.disabled=!1,n.style.display="none"}};Utils.lpFetchAPI(Utils.lpAddQueryArgs(Utils.lpGetCurrentURLNoParam(),r),{},o)})(o,a),((e,t)=>{if("SPAN"!==t.tagName)return;if(!t.closest("#learn-press-order"))return;if(e.preventDefault(),!confirm("Are you sure you want to remove this item?"))return;t.disabled=!0,t.classList.add("dashicons-update");const r=t.closest(".order-item-row"),o=t.closest(".list-order-items"),n=parseInt(r.getAttribute("data-item_id")),a=parseInt(r.getAttribute("data-id")),l={"lp-ajax":"remove_items_from_order",order_id:document.querySelector("#post_ID").value,items:n,nonce:lpDataAdmin.nonce},i={success(e){const{data:t,messages:r,status:n}=e;if("error"===n)return void console.error(r);const{item_html:l,order_data:i}=t,c=o.querySelector(".no-order-items");o.querySelectorAll(".order-item-row").forEach((e=>{e.remove()})),l.length?c.insertAdjacentHTML("beforebegin",l):c.style.display="block",p.splice(p.indexOf(a),1),m.splice(p.indexOf(a),1),s.querySelector(".order-subtotal").innerHTML=i.subtotal_html,s.querySelector(".order-total").innerHTML=i.total_html},error(e){console.error(e)},completed(){}};Utils.lpFetchAPI(Utils.lpAddQueryArgs(Utils.lpGetCurrentURLNoParam(),l),{},i)})(o,a)})),document.addEventListener("keyup",(function(e){((e,t)=>{if("search"!==t.name)return;if(!t.closest(d))return;e.preventDefault();const r=t.value;(!r||r&&r.length>2)&&(void 0!==c&&clearTimeout(c),c=setTimeout((function(){f(r,m,1)}),800))})(e,e.target)})),document.addEventListener("DOMContentLoaded",(()=>{s=document.querySelector("#learn-press-order"),i=s.querySelector(".list-order-items"),t=s.querySelector("#learn-press-add-order-item"),o=document.querySelector("#learn-press-modal-search-items"),n=document.querySelector("#container-modal-search-items"),s&&t&&(document.querySelectorAll("#learn-press-order .list-order-items tbody .order-item-row").forEach((e=>{const t=parseInt(e.getAttribute("data-id"));m.push(t)})),n.innerHTML=o.innerHTML,e=n.querySelector(d),r=e.querySelector(".search-results"),a=e.querySelector("footer"),l=a.querySelector(".add"),n.style.display="none")}))};export default addCoursesToOrder;