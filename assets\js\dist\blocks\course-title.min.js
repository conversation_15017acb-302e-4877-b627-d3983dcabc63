(()=>{"use strict";const e=window.React,t=window.wp.i18n,l=window.wp.blockEditor,n=(window.wp.element,window.wp.components),r=r=>{const{attributes:a,setAttributes:s,context:o}=r,{tag:i,isLink:u,target:p}=a,c=(0,l.useBlockProps)(),{lpCourseData:m}=o,g=m?.title||(0,t.__)("Course Title","learnpress"),d=i;return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.Inspector<PERSON>ontrols,null,(0,e.createElement)(n.PanelBody,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(n.SelectControl,{label:(0,t.__)("Tag","learnpress"),value:i,options:[{label:"span",value:"span"},{label:"div",value:"div"},{label:"h1",value:"h1"},{label:"h2",value:"h2"},{label:"h3",value:"h3"},{label:"h4",value:"h4"},{label:"h5",value:"h5"},{label:"h6",value:"h6"}],onChange:e=>s({tag:e})}),(0,e.createElement)(n.ToggleControl,{label:(0,t.__)("Make the title a link"),checked:!!u,onChange:e=>{r.setAttributes({isLink:e})}}),r.attributes.isLink?(0,e.createElement)(n.ToggleControl,{label:(0,t.__)("Open is new tab","learnpress"),checked:!!p,onChange:e=>{r.setAttributes({target:e})}}):"")),(0,e.createElement)("div",{...c},(0,e.createElement)(d,{dangerouslySetInnerHTML:{__html:g}})))},a=e=>null,s=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-title","title":"Course Title","category":"learnpress-course-elements","description":"Course title.","textdomain":"learnpress","keywords":["course title","learnpress"],"ancestor":["learnpress/single-course","learnpress/course-item-template"],"attributes":{"tag":{"type":"string","default":"span"},"isLink":{"type":"boolean","default":false},"target":{"type":"boolean","default":false}},"usesContext":["lpCourseData"],"supports":{"multiple":true,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"textTransform":false,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":true,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true,"textTransform":false}},"color":{"text":true,"link":true,"background":true,"__experimentalDefaultControls":{"text":true,"link":false,"background":false}},"__experimentalBorder":{"color":false,"radius":false,"width":false},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),o=window.wp.blocks,i=window.wp.data;let u=null;const p=window.wp.primitives,c=window.ReactJSXRuntime,m=(0,c.jsx)(p.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(p.Path,{d:"m4 5.5h2v6.5h1.5v-6.5h2v-1.5h-5.5zm16 10.5h-16v-1.5h16zm-7 4h-9v-1.5h9z"})});var g,d,h;g=["learnpress/learnpress//single-lp_course"],d=s,h=e=>{(0,o.registerBlockType)(e.name,{...e,icon:m,edit:r,save:a})},(0,i.subscribe)((()=>{const e={...d},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const l=t.getCurrentPostId();null!==l&&u!==l&&(u=l,(0,o.getBlockType)(e.name)&&((0,o.unregisterBlockType)(e.name),g.includes(l)?(e.ancestor=null,h(e)):(e.ancestor||(e.ancestor=[]),h(e))))})),(0,o.registerBlockType)(s.name,{...s,icon:m,edit:r,save:a})})();