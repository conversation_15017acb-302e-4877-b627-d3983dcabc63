(()=>{var e={335:()=>{var e;e=jQuery,window.FIB={getSelectedText:function(){let e="";if(void 0!==window.getSelection){const t=window.getSelection();if(t.rangeCount){const n=document.createElement("div");for(let e=0,o=t.rangeCount;e<o;++e)n.appendChild(t.getRangeAt(e).cloneContents());e=n.innerHTML}}else void 0!==document.selection&&"Text"===document.selection.type&&(e=document.selection.createRange().htmlText);return e},createTextNode:e=>document.createTextNode(e),isContainHtml:function(t){const n=e(t),o="b.fib-blank";return n.is(o)||n.find(o).length||n.parent().is(o)},getSelectionRange:function(){let e="";return window.getSelection?e=window.getSelection():document.getSelection?e=document.getSelection():document.selection&&(e=document.selection.createRange().text),e},outerHTML:t=>e("<div>").append(e(t).clone()).html(),doUpgrade(t){e.ajax({url:"",data:{"lp-ajax":"fib-upgrade"},success(e){console.log(e),t&&t.call(e)}})}},e(document).ready((function(){e("#do-upgrade-fib").on("click",(function(){const t=e(this).prop("disabled",!0).addClass("ajaxloading");FIB.doUpgrade((function(){t.prop("disabled",!1).removeClass("ajaxloading")}))}))}))}},t={};function n(o){var s=t[o];if(void 0!==s)return s.exports;var r=t[o]={exports:{}};return e[o](r,r.exports,n),r.exports}(()=>{"use strict";const e={id:function(e){return e.id},type:function(e){return e.type},code:function(e){return Date.now()},autoDraft:function(e){return e.auto_draft},answers:function(e){return Object.values(e.answers)||[]},settings:function(e){return e.setting},types:function(e){return e.questionTypes||[]},numberCorrect:function(e){var t=0;return Object.keys(e.answers).forEach((function(n){"yes"===e.answers[n].is_true&&(t+=1)})),t},status:function(e){return e.status},currentRequest:function(e){return e.countCurrentRequest||0},action:function(e){return e.action},nonce:function(e){return e.nonce},externalComponent:function(e){return e.externalComponent||[]},supportAnswerOptions:function(e){return e.supportAnswerOptions||[]},state:function(e){return e},i18n:function(e){return e.i18n}},t={UPDATE_STATUS:function(e,t){e.status=t},UPDATE_AUTO_DRAFT_STATUS:function(e,t){e.auto_draft=t},CHANGE_QUESTION_TYPE:function(e,t){e.answers=t.answers,e.type=t.type},SET_ANSWERS:function(e,t){e.answers=t},DELETE_ANSWER:function(e,t){for(var n=0,o=e.answers.length;n<o;n++)if(e.answers[n].question_answer_id==t){e.answers[n].question_answer_id=LP.uniqueId();break}},ADD_NEW_ANSWER:function(e,t){e.answers.push(t)},UPDATE_ANSWERS:function(e,t){e.answers=t},INCREASE_NUMBER_REQUEST:function(e){e.countCurrentRequest++},DECREASE_NUMBER_REQUEST:function(e){e.countCurrentRequest--}},o=t,s={changeQuestionType(e,t){const n=void 0!==t.question?t.question:"";LP.Request({type:"change-question-type",question_type:t.type,draft_question:e.getters.autoDraft?n:""}).then((function(t){const n=t.body;n.success&&(e.commit("UPDATE_AUTO_DRAFT_STATUS",!1),e.commit("CHANGE_QUESTION_TYPE",n.data))}))},updateAnswersOrder(e,t){LP.Request({type:"sort-answer",order:t}).then((function(e){e.body.success}))},updateAnswerTitle(e,t){void 0!==t.question_answer_id&&(t=JSON.stringify(t),LP.Request({type:"update-answer-title",answer:t}))},updateCorrectAnswer(e,t){LP.Request({type:"change-correct",correct:JSON.stringify(t)}).then((function(t){const n=t.body;n.success&&(e.commit("UPDATE_ANSWERS",n.data),e.commit("UPDATE_AUTO_DRAFT_STATUS",!1))}))},deleteAnswer(e,t){e.commit("DELETE_ANSWER",t.id),LP.Request({type:"delete-answer",answer_id:t.id}).then((function(t){const n=t.body;n.success&&e.commit("SET_ANSWERS",n.data)}))},newAnswer(e,t){e.commit("ADD_NEW_ANSWER",t.answer),LP.Request({type:"new-answer"}).then((function(t){const n=t.body;n.success&&e.commit("UPDATE_ANSWERS",n.data)}))},newRequest(e){e.commit("INCREASE_NUMBER_REQUEST"),e.commit("UPDATE_STATUS","loading"),window.onbeforeunload=function(){return""}},requestCompleted(e,t){e.commit("DECREASE_NUMBER_REQUEST"),0===e.getters.currentRequest&&(e.commit("UPDATE_STATUS",t),window.onbeforeunload=null)}},r=window.jQuery||jQuery;n(335),window.$Vue=window.$Vue||Vue,window.$Vuex=window.$Vuex||Vuex;const i=window.jQuery;i(document).ready((function(){var t;window.LP_Question_Store=new $Vuex.Store((t=lp_question_editor,{state:r.extend({status:"successful",countCurrentRequest:0,i18n:r.extend({},t.i18n)},t.root),getters:e,mutations:o,actions:s})),function(e){const t=window.jQuery||jQuery,n=Vue.http;e=t.extend({ns:"LPRequest",store:!1},e||{});let o=null;LP.Request=function(s){return o=t("#publishing-action"),s.id=e.store.getters.id,s.nonce=e.store.getters.nonce,s["lp-ajax"]=e.store.getters.action,o.find("#publish").addClass("disabled"),o.find(".spinner").addClass("is-active"),o.addClass("code-"+s.code),n.post(e.store.getters.urlAjax,s,{emulateJSON:!0,params:{namespace:e.ns,code:s.code}})},n.interceptors.push((function(t,n){t.params.namespace===e.ns?(e.store.dispatch("newRequest"),n((function(n){jQuery.isPlainObject(n.body)||(n.body=LP.parseJSON(n.body)),n.body.success?e.store.dispatch("requestCompleted","successful"):e.store.dispatch("requestCompleted","failed"),o.removeClass("code-"+t.params.code),o.attr("class")||(o.find("#publish").removeClass("disabled"),o.find(".spinner").removeClass("is-active"))}))):n()}))}({ns:"LPQuestionEditorRequest",store:LP_Question_Store}),setTimeout((()=>{i("#admin-editor-lp_question").length&&(window.LP_Question_Editor=new $Vue({el:"#admin-editor-lp_question",template:"<lp-question-editor></lp-question-editor>"}))}),100)}))})()})();