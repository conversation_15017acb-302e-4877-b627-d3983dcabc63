@charset "UTF-8";
/**
 * Mixin
 */
@-webkit-keyframes rotating4 {
  from {
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes rotating4 {
  from {
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes animation4 {
  from {
    left: -40%;
    width: 40%;
  }
  to {
    left: 100%;
    width: 10%;
  }
}
@keyframes animation4 {
  from {
    left: -40%;
    width: 40%;
  }
  to {
    left: 100%;
    width: 10%;
  }
}
:root {
  --lp-cotainer-max-with: var(--lp-container-max-width);
}

.wp-block-group {
  --lp-container-max-width: var(--wp--style--global--wide-size);
}

@font-face {
  font-family: "lp-icon";
  src: url("../src/css/vendor/fonts/lp-icon/lp-icon.woff2?v=45") format("woff2");
  font-weight: normal;
  font-style: normal;
  font-display: block;
}
[class^=lp-icon-], [class*=" lp-icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "lp-icon";
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.lp-icon-plus:before {
  content: "\f067";
}

.lp-icon-question:before {
  content: "\f128";
}

.lp-icon-minus:before {
  content: "\f068";
}

.lp-icon-search:before {
  content: "\f002";
}

.lp-icon-envelope-o:before {
  content: "\f003";
}

.lp-icon-heart:before {
  content: "\f004";
}

.lp-icon-star:before {
  content: "\f005";
}

.lp-icon-star-o:before {
  content: "\f006";
}

.lp-icon-user:before {
  content: "\f007";
}

.lp-icon-th-large:before {
  content: "\f009";
}

.lp-icon-th:before {
  content: "\f00a";
}

.lp-icon-th-list:before {
  content: "\f00b";
}

.lp-icon-check:before {
  content: "\f00c";
}

.lp-icon-close:before {
  content: "\f00d";
}

.lp-icon-remove:before {
  content: "\f00d";
}

.lp-icon-times:before {
  content: "\f00d";
}

.lp-icon-signal:before {
  content: "\f012";
}

.lp-icon-cog:before {
  content: "\f013";
}

.lp-icon-gear:before {
  content: "\f013";
}

.lp-icon-trash-o:before {
  content: "\f014";
}

.lp-icon-home:before {
  content: "\f015";
}

.lp-icon-file-o:before {
  content: "\f016";
}

.lp-icon-clock-o:before {
  content: "\f017";
}

.lp-icon-download:before {
  content: "\f019";
}

.lp-icon-play-circle-o:before {
  content: "\f01d";
}

.lp-icon-refresh:before {
  content: "\f021";
}

.lp-icon-lock:before {
  content: "\f023";
}

.lp-icon-flag:before {
  content: "\f024";
}

.lp-icon-book:before {
  content: "\f02d";
}

.lp-icon-bookmark:before {
  content: "\f02e";
}

.lp-icon-video-camera:before {
  content: "\f03d";
}

.lp-icon-edit:before {
  content: "\f044";
}

.lp-icon-pencil-square-o:before {
  content: "\f044";
}

.lp-icon-check-square-o:before {
  content: "\f046";
}

.lp-icon-arrows:before {
  content: "\f047";
}

.lp-icon-chevron-left:before {
  content: "\f053";
}

.lp-icon-chevron-right:before {
  content: "\f054";
}

.lp-icon-check-circle:before {
  content: "\f058";
}

.lp-icon-question-circle:before {
  content: "\f059";
}

.lp-icon-arrow-left:before {
  content: "\f060";
}

.lp-icon-arrow-right:before {
  content: "\f061";
}

.lp-icon-expand:before {
  content: "\f065";
}

.lp-icon-compress:before {
  content: "\f066";
}

.lp-icon-exclamation-circle:before {
  content: "\f06a";
}

.lp-icon-gift:before {
  content: "\f06b";
}

.lp-icon-eye:before {
  content: "\f06e";
}

.lp-icon-eye-slash:before {
  content: "\f070";
}

.lp-icon-exclamation-triangle:before {
  content: "\f071";
}

.lp-icon-warning:before {
  content: "\f071";
}

.lp-icon-calendar:before {
  content: "\f073";
}

.lp-icon-comment:before {
  content: "\f075";
}

.lp-icon-chevron-up:before {
  content: "\f077";
}

.lp-icon-chevron-down:before {
  content: "\f078";
}

.lp-icon-shopping-cart:before {
  content: "\f07a";
}

.lp-icon-bar-chart:before {
  content: "\f080";
}

.lp-icon-bar-chart-o:before {
  content: "\f080";
}

.lp-icon-key:before {
  content: "\f084";
}

.lp-icon-cogs:before {
  content: "\f085";
}

.lp-icon-gears:before {
  content: "\f085";
}

.lp-icon-thumbs-o-up:before {
  content: "\f087";
}

.lp-icon-thumbs-o-down:before {
  content: "\f088";
}

.lp-icon-heart-o:before {
  content: "\f08a";
}

.lp-icon-sign-out:before {
  content: "\f08b";
}

.lp-icon-linkedin-square:before {
  content: "\f08c";
}

.lp-icon-sign-in:before {
  content: "\f090";
}

.lp-icon-phone:before {
  content: "\f095";
}

.lp-icon-square-o:before {
  content: "\f096";
}

.lp-icon-bookmark-o:before {
  content: "\f097";
}

.lp-icon-twitter:before {
  content: "\f099";
}

.lp-icon-facebook:before {
  content: "\f09a";
}

.lp-icon-facebook-f:before {
  content: "\f09a";
}

.lp-icon-unlock:before {
  content: "\f09c";
}

.lp-icon-bullhorn:before {
  content: "\f0a1";
}

.lp-icon-bell-o:before {
  content: "\f0a2";
}

.lp-icon-certificate:before {
  content: "\f0a3";
}

.lp-icon-briefcase:before {
  content: "\f0b1";
}

.lp-icon-arrows-alt:before {
  content: "\f0b2";
}

.lp-icon-copy:before {
  content: "\f0c5";
}

.lp-icon-files-o:before {
  content: "\f0c5";
}

.lp-icon-pinterest-square:before {
  content: "\f0d3";
}

.lp-icon-google-plus-square:before {
  content: "\f0d4";
}

.lp-icon-google-plus:before {
  content: "\f0d5";
}

.lp-icon-money-bill-alt:before {
  content: "\f0d6";
}

.lp-icon-caret-down:before {
  content: "\f0d7";
}

.lp-icon-caret-up:before {
  content: "\f0d8";
}

.lp-icon-caret-left:before {
  content: "\f0d9";
}

.lp-icon-caret-right:before {
  content: "\f0da";
}

.lp-icon-linkedin:before {
  content: "\f0e1";
}

.lp-icon-comment-o:before {
  content: "\f0e5";
}

.lp-icon-file-text-o:before {
  content: "\f0f6";
}

.lp-icon-angle-double-left:before {
  content: "\f100";
}

.lp-icon-angle-double-right:before {
  content: "\f101";
}

.lp-icon-angle-double-up:before {
  content: "\f102";
}

.lp-icon-angle-double-down:before {
  content: "\f103";
}

.lp-icon-angle-left:before {
  content: "\f104";
}

.lp-icon-angle-right:before {
  content: "\f105";
}

.lp-icon-angle-up:before {
  content: "\f106";
}

.lp-icon-angle-down:before {
  content: "\f107";
}

.lp-icon-desktop:before {
  content: "\f108";
}

.lp-icon-mobile:before {
  content: "\f10b";
}

.lp-icon-mobile-phone:before {
  content: "\f10b";
}

.lp-icon-circle-o:before {
  content: "\f10c";
}

.lp-icon-spinner:before {
  content: "\f110";
}

.lp-icon-code:before {
  content: "\f121";
}

.lp-icon-puzzle-piece:before {
  content: "\f12e";
}

.lp-icon-calendar-o:before {
  content: "\f133";
}

.lp-icon-ellipsis-h:before {
  content: "\f141";
}

.lp-icon-ellipsis-v:before {
  content: "\f142";
}

.lp-icon-file:before {
  content: "\f15b";
}

.lp-icon-file-alt:before {
  content: "\f15c";
}

.lp-icon-youtube-play:before {
  content: "\f16a";
}

.lp-icon-instagram:before {
  content: "\f16d";
}

.lp-icon-dot-circle-o:before {
  content: "\f192";
}

.lp-icon-graduation-cap:before {
  content: "\f19d";
}

.lp-icon-mortar-board:before {
  content: "\f19d";
}

.lp-icon-google:before {
  content: "\f1a0";
}

.lp-icon-language:before {
  content: "\f1ab";
}

.lp-icon-database:before {
  content: "\f1c0";
}

.lp-icon-file-pdf:before {
  content: "\f1c1";
}

.lp-icon-file-word:before {
  content: "\f1c2";
}

.lp-icon-file-excel:before {
  content: "\f1c3";
}

.lp-icon-file-powerpoint:before {
  content: "\f1c4";
}

.lp-icon-file-image:before {
  content: "\f1c5";
}

.lp-icon-file-photo:before {
  content: "\f1c5";
}

.lp-icon-file-picture:before {
  content: "\f1c5";
}

.lp-icon-file-archive:before {
  content: "\f1c6";
}

.lp-icon-file-zip:before {
  content: "\f1c6";
}

.lp-icon-file-audio:before {
  content: "\f1c7";
}

.lp-icon-file-sound:before {
  content: "\f1c7";
}

.lp-icon-file-movie:before {
  content: "\f1c8";
}

.lp-icon-file-video:before {
  content: "\f1c8";
}

.lp-icon-file-code:before {
  content: "\f1c9";
}

.lp-icon-circle-thin:before {
  content: "\f1db";
}

.lp-icon-share-alt:before {
  content: "\f1e0";
}

.lp-icon-pie-chart:before {
  content: "\f200";
}

.lp-icon-line-chart:before {
  content: "\f201";
}

.lp-icon-user-secret:before {
  content: "\f21b";
}

.lp-icon-television:before {
  content: "\f26c";
}

.lp-icon-tv:before {
  content: "\f26c";
}

.lp-icon-question-circle-o:before {
  content: "\f29c";
}

.lp-icon-google-plus-circle:before {
  content: "\f2b3";
}

.lp-icon-google-plus-official:before {
  content: "\f2b3";
}

.lp-icon-user-circle:before {
  content: "\f2bd";
}

.lp-icon-user-graduate:before {
  content: "\f501";
}

.lp-icon-user-edit:before {
  content: "\f4ff";
}

.lp-icon-my-courses:before {
  content: "\e900";
}

.lp-icon-clock:before {
  content: "\e901";
}

.lp-icon-stopwatch:before {
  content: "\f2f2";
}

.lp-icon-file-download:before {
  content: "\f56d";
}

.lp-icon-list:before {
  content: "\f03a";
}

.lp-icon-students:before {
  content: "\e902";
}

.lp-icon-courses:before {
  content: "\e903";
}

.lp-icon-tiktok-alt:before {
  content: "\e908";
}

.lp-icon-alarm-clock:before {
  content: "\e909";
}

.lp-icon-art-and-design:before {
  content: "\e90a";
}

.lp-icon-best-customer-experience:before {
  content: "\e90b";
}

.lp-icon-certificate-o:before {
  content: "\e90c";
}

.lp-icon-community:before {
  content: "\e90d";
}

.lp-icon-online-education:before {
  content: "\e90e";
}

.lp-icon-concierge-bell:before {
  content: "\e90f";
}

.lp-icon-target:before {
  content: "\e910";
}

.lp-icon-content-marketing:before {
  content: "\e911";
}

.lp-icon-dollar-sign:before {
  content: "\e912";
}

.lp-icon-website:before {
  content: "\e913";
}

.lp-icon-worldwide-security:before {
  content: "\e914";
}

.lp-icon-educational-programs:before {
  content: "\e915";
}

.lp-icon-online-class:before {
  content: "\e916";
}

.lp-icon-monitor:before {
  content: "\e917";
}

.lp-icon-lucide:before {
  content: "\e918";
}

.lp-icon-ebook:before {
  content: "\e919";
}

.lp-icon-co-instructor:before {
  content: "\e904";
}

.lp-icon-close-circle:before {
  content: "\e907";
}

.lp-icon-layers:before {
  content: "\e91a";
}

.lp-icon-filter:before {
  content: "\e91c";
}

.lp-icon-in_progress_course:before {
  content: "\e91d";
}

.lp-icon-finished_courses:before {
  content: "\e91e";
}

.lp-icon-failed_courses:before {
  content: "\e91f";
}

.lp-icon-passed_courses:before {
  content: "\e920";
}

.lp-icon-student_in_progress:before {
  content: "\e924";
}

.lp-icon-student_completed:before {
  content: "\e925";
}

.lp-icon-published_course:before {
  content: "\e926";
}

.lp-icon-pending_course:before {
  content: "\e927";
}

.lp-icon-list-check:before {
  content: "\e929";
}

.lp-icon-icon-h5p:before {
  content: "\e92a";
}

.lp-icon-comment-written:before {
  content: "\e921";
}

.lp-icon-book-open:before {
  content: "\f518";
}

.lp-icon-pinterest-p:before {
  content: "\f231";
}

.lp-icon-dropbox:before {
  content: "\e905";
}

.lp-icon-edit-square:before {
  content: "\e923";
}

.lp-icon-drag:before {
  content: "\e922";
}

.lp-icon-map-pin:before {
  content: "\e91b";
}

.lp-icon-map-marker:before {
  content: "\e91b";
}

.lp-icon-box:before {
  content: "\e906";
}

.lp-skeleton-animation {
  margin: 0;
  padding: 0;
  list-style: none;
}
.lp-skeleton-animation > li {
  width: 100%;
  height: 16px;
  margin-top: 15px;
  border-radius: 2px;
  background: linear-gradient(90deg, hsla(0, 0%, 74.5%, 0.2) 25%, hsla(0, 0%, 50.6%, 0.24) 37%, hsla(0, 0%, 74.5%, 0.2) 63%);
  background-size: 400% 100%;
  list-style: none;
  animation: lp-skeleton-loading 1.4s ease infinite;
}

@keyframes lp-skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  to {
    background-position: 0 50%;
  }
}
.lp-loading-circle {
  width: 15px;
  height: 15px;
  border: 2px solid #ccc;
  border-radius: 50%;
  border-top-color: rgba(0, 0, 0, 0.76);
  animation: spin 1s infinite linear;
  display: inline-block;
}
.lp-loading-circle.hide {
  display: none;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/**
* Style for course filter
*
* @since 4.2.3.2
* @version 1.0.2
*/
.lp-form-course-filter__title {
  font-weight: 500;
  margin-bottom: 12px;
}
.lp-form-course-filter__content {
  position: relative;
}
.lp-form-course-filter__content .lp-course-filter__field * {
  cursor: pointer;
}
.lp-form-course-filter__content .lp-course-filter__field {
  position: relative;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}
.lp-form-course-filter__content .lp-course-filter__field input:focus {
  outline: none;
}
.lp-form-course-filter__content .lp-course-filter__field input[disabled] {
  cursor: not-allowed;
}
.lp-form-course-filter__content .lp-course-filter__field label {
  margin: 0;
  text-transform: capitalize;
  line-height: inherit;
  flex: 1;
}
.lp-form-course-filter__content .lp-course-filter__field .count {
  color: #999;
}
.lp-form-course-filter__content .lp-course-filter-search-field {
  position: relative;
  display: flex;
  align-items: center;
}
.lp-form-course-filter__content .lp-course-filter-search-field input {
  width: 100%;
  line-height: 2rem;
  border: 1px solid #eee;
  padding: 8px;
}
.lp-form-course-filter__content .lp-course-filter-search-field input:focus {
  outline: none;
}
.lp-form-course-filter__content .lp-course-filter-search-field .lp-loading-circle {
  position: absolute;
  right: 10px;
}
.lp-form-course-filter__content .lp-course-filter-search-result {
  position: absolute;
  z-index: 10;
  background: white;
  width: 100%;
  border: 1px solid #eeeeee;
  max-height: 300px;
  overflow-y: auto;
  padding: 0;
}
.lp-form-course-filter__content .lp-course-filter-search-result:empty {
  border: none;
}
.lp-form-course-filter__content .lp-course-filter-search-result .lp-courses-suggest-list {
  padding: 0 10px;
}
.lp-form-course-filter__content .lp-course-filter-search-result .lp-courses-suggest-list .item-course-suggest {
  display: flex;
  gap: 8px;
  padding: 10px 0;
  align-items: center;
  border-bottom: 1px solid #f5f5f5;
}
.lp-form-course-filter__content .lp-course-filter-search-result .lp-courses-suggest-list .item-course-suggest:last-child {
  border-bottom: none;
}
.lp-form-course-filter__content .lp-course-filter-search-result .lp-courses-suggest-list .course-img {
  width: 50px;
}
.lp-form-course-filter__content .lp-course-filter-search-result .lp-courses-suggest-list .course-img img {
  width: 50px;
  height: auto;
  display: block;
}
.lp-form-course-filter__content .lp-course-filter-search-result .lp-courses-suggest-list a {
  font-size: 0.875em;
  line-height: 1em;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.lp-form-course-filter__content .lp-course-filter-search-result .lp-courses-suggest-list a span {
  font-size: 0.875em;
  line-height: 1em;
}
.lp-form-course-filter__content .lp-course-filter-search-result .lp-courses-suggest-info {
  border-top: 1px solid #eeeeee;
  padding: 10px;
  background: #e8f0fe;
  font-weight: 500;
  display: flex;
  justify-content: space-between;
}
.lp-form-course-filter .course-filter-submit, .lp-form-course-filter .course-filter-reset {
  padding: 12px 24px;
  text-transform: capitalize;
  width: calc(50% - 4px);
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: var(--lp-button-background-color);
  color: var(--lp-button-color);
}
.lp-form-course-filter .course-filter-submit:focus, .lp-form-course-filter .course-filter-reset:focus {
  outline: none;
}
.lp-form-course-filter .course-filter-submit:hover, .lp-form-course-filter .course-filter-reset:hover {
  color: #fff;
  background: var(--lp-primary-color);
}
.lp-form-course-filter .course-filter-reset {
  margin-left: 4px;
}
.lp-form-course-filter .course-filter-submit {
  margin-right: 4px;
}
.lp-form-course-filter .lp-form-course-filter__item {
  margin-bottom: 12px;
  border-bottom: 1px solid #ccc;
  padding-bottom: 12px;
}
.lp-form-course-filter .lp-form-course-filter__item .lp-cate-parent .lp-cate-child {
  margin-left: 1rem;
}

#learn-press-become-teacher-form .become-teacher-fields {
  list-style: none;
  padding: 0;
}

.lp-form-course-filter__close, .elementor-widget-container .lp-form-course-filter__close {
  display: none;
}

.elementor-widget-container .learnpress-widget-wrapper {
  position: unset;
}

@media (max-width: 768px) {
  .widget_course_filter {
    position: fixed !important;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 999999;
    width: 400px;
    height: 100%;
    background-color: #fff;
    transition: 0.3s;
    display: block;
    overflow-y: auto;
    border: none !important;
    border-radius: 0 !important;
    transform: translate3d(100%, 0, 0);
    margin: 0;
    opacity: 0;
    visibility: hidden;
    padding: 20px;
  }
  .widget_course_filter .lp-form-block-course-filter .course-filter-submit,
  .widget_course_filter .lp-form-course-filter .course-filter-submit {
    width: 100%;
    margin: 0;
  }
  .widget_course_filter .lp-form-block-course-filter .course-filter-reset,
  .widget_course_filter .lp-form-course-filter .course-filter-reset {
    width: 100%;
    margin: 12px 0 0 0;
  }
  .widget_course_filter .widget-title {
    display: flex;
    gap: 16px;
    align-items: center;
    font-size: calc(var(--lp-font-size-base, 1em) * 1.25);
  }
  .show-lp-course-filter-mobile {
    overflow: hidden;
  }
  .show-lp-course-filter-mobile .widget_course_filter {
    transform: none;
    opacity: 1;
    visibility: visible;
  }
  .show-lp-course-filter-mobile .lp-archive-courses-sidebar::after {
    position: fixed;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    content: "";
    -webkit-transition: opacity 0.5s, width 0.1s 0.5s, height 0.1s 0.5s;
    transition: opacity 0.5s, width 0.1s 0.5s, height 0.1s 0.5s;
    z-index: 99999;
  }
  .show-lp-course-filter-mobile .learnpress-widget-wrapper {
    position: unset;
  }
  .lp-form-course-filter__close, .elementor-widget-container .lp-form-course-filter__close {
    display: flex;
    position: absolute;
    right: 0;
    top: 20px;
    left: auto;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 0 20px;
  }
  .lp-form-course-filter__close i, .elementor-widget-container .lp-form-course-filter__close i {
    font-size: 1rem;
  }
}
@media (max-width: 500px) {
  .widget_course_filter {
    width: 100%;
    left: 0;
    right: 0;
    box-sizing: border-box;
  }
}
@keyframes lp-rotating {
  from {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes lp-rotating {
  from {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.lp-loading-change {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.38);
  top: 0;
}

.lp-load-ajax-element {
  position: relative;
}

@media (min-width: 1024px) {
  body.theme-divi {
    --lp-cotainer-padding:0;
  }
}
.learnpress.widget {
  margin-bottom: 30px;
}
.learnpress.widget h3 {
  margin-bottom: 20px;
}
.learnpress.widget a {
  text-decoration: none;
}

.learnpress-widget-wrapper {
  margin-top: 16px;
  position: relative;
}
.learnpress-widget-wrapper .lp-widget-loading-change {
  display: none;
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.38);
  top: 0;
  z-index: 10;
}

.learnpress-widget-wrapper .lp-widget-course {
  margin: 0 0 30px 0;
  border: 1px solid #eee;
}
.learnpress-widget-wrapper .lp-widget-course__image img {
  height: auto;
  width: 100%;
  display: block;
}
.learnpress-widget-wrapper .lp-widget-course__title {
  margin-bottom: 20px;
  font-size: 1em;
  font-weight: 600;
}
.learnpress-widget-wrapper .lp-widget-course__content {
  margin: 0;
  padding: 16px;
  border-top: 1px solid #eee;
}
.learnpress-widget-wrapper .lp-widget-course__title {
  margin: 4px 0 16px 0;
}
.learnpress-widget-wrapper .lp-widget-course__meta {
  display: grid;
  margin: 16px 0 0 0;
  grid-template-columns: repeat(2, 1fr);
  justify-content: space-between;
  align-items: center;
  gap: 24px;
}
.learnpress-widget-wrapper .lp-widget-course__meta .course-price {
  margin-bottom: 0;
}
.learnpress-widget-wrapper .lp-widget-course__meta .course-price .course-item-price {
  display: flex;
  flex-direction: column;
}
.learnpress-widget-wrapper .lp-widget-course__description {
  color: #666;
  font-size: 0.875em;
}
.learnpress-widget-wrapper .lp-widget-course__price {
  color: #444;
  font-size: 0.875em;
  font-weight: 600;
}
.learnpress-widget-wrapper .lp-widget-course__instructor {
  display: flex;
  align-items: center;
}
.learnpress-widget-wrapper .lp-widget-course__instructor__avatar {
  margin-right: 5px;
}
.learnpress-widget-wrapper .lp-widget-course__instructor__avatar img {
  width: 20px;
  border-radius: 20px;
  height: 20px;
  display: block;
}
.learnpress-widget-wrapper .lp-widget-course__instructor > a {
  color: #777;
  font-size: 0.875em;
}

.learnpress.widget_course_progress .lp_widget_course_progress,
.elementor-widget-wp-widget-learnpress_widget_course_progress .lp_widget_course_progress {
  margin: 0;
  padding: 20px;
  border: 1px solid #eee;
}
.learnpress.widget_course_progress .course-results-progress,
.elementor-widget-wp-widget-learnpress_widget_course_progress .course-results-progress {
  margin-top: 10px;
  font-size: 0.8em;
}
.learnpress.widget_course_progress .course-results-progress .items-progress,
.elementor-widget-wp-widget-learnpress_widget_course_progress .course-results-progress .items-progress {
  display: flex;
  margin-bottom: 7px;
  justify-content: space-between;
  align-items: center;
}
.learnpress.widget_course_progress .course-results-progress .items-progress__heading,
.elementor-widget-wp-widget-learnpress_widget_course_progress .course-results-progress .items-progress__heading {
  font-weight: 400;
}
.learnpress.widget_course_progress .lp-course-progress,
.elementor-widget-wp-widget-learnpress_widget_course_progress .lp-course-progress {
  width: 100%;
}
.learnpress.widget_course_progress .lp-course-progress.learn-press-progress,
.elementor-widget-wp-widget-learnpress_widget_course_progress .lp-course-progress.learn-press-progress {
  position: relative;
}
.learnpress.widget_course_progress .lp-course-progress.learn-press-progress .progress-bg,
.elementor-widget-wp-widget-learnpress_widget_course_progress .lp-course-progress.learn-press-progress .progress-bg {
  overflow: hidden;
  position: relative;
  height: 6px;
  background: #ccc;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
}
.learnpress.widget_course_progress .lp-course-progress.learn-press-progress .progress-bg .progress-active,
.elementor-widget-wp-widget-learnpress_widget_course_progress .lp-course-progress.learn-press-progress .progress-bg .progress-active {
  position: absolute;
  left: 50%;
  width: 100%;
  height: 100%;
  margin-left: -100%;
  background: var(--lp-primary-color);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
}
.learnpress.widget_course_progress .lp-course-progress.learn-press-progress .lp-passing-conditional,
.elementor-widget-wp-widget-learnpress_widget_course_progress .lp-course-progress.learn-press-progress .lp-passing-conditional {
  position: absolute;
  top: 0;
  width: 3px;
  height: 6px;
  margin-left: -1px;
  background: var(--lp-secondary-color);
}

.lp_widget_course_info {
  margin: 0;
  padding: 20px;
  border: 1px solid var(--lp-border-color, #E2E0DB);
}
.lp_widget_course_info h3 {
  margin-bottom: 15px;
}
.lp_widget_course_info ul {
  margin: 0;
  padding: 0;
}
.lp_widget_course_info ul li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 5px;
}
.lp_widget_course_info ul label {
  font-size: var(--lp-font-size-base, 1em);
  margin: 0;
}

.lp-widget-course-extra {
  margin: 0;
}
.lp-widget-course-extra h3 {
  margin-bottom: 20px;
  font-size: var(--lp-font-size-base, 1em);
}
.lp-widget-course-extra__content h4.course-extras__title {
  margin: 0;
  margin-bottom: 15px;
  font-size: var(--lp-font-size-base, 1em);
  font-weight: 600;
}
.lp-widget-course-extra__content ul {
  margin: 0;
  padding: 0;
}
.lp-widget-course-extra__content ul li {
  margin-bottom: 10px;
}
.lp-widget-course-extra__content .course-extras {
  display: block;
}
.lp-widget-course-extra__content .course-extras .course-extras__content ul {
  list-style: none;
}
.lp-widget-course-extra__content .course-extras .course-extras__content ol {
  list-style-position: inside;
}
.lp-widget-course-extra__content .course-extras .course-extras__content ul li,
.lp-widget-course-extra__content .course-extras .course-extras__content ol li {
  position: relative;
  border: none;
}
.lp-widget-course-extra__content .course-extras.style-checks ul li,
.lp-widget-course-extra__content .course-extras.style-checks ol li {
  padding-left: 20px;
}
.lp-widget-course-extra__content .course-extras.style-checks ul li::before,
.lp-widget-course-extra__content .course-extras.style-checks ol li::before {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 0;
  width: 12px;
  height: 7px;
  border: 2px solid #8794b9;
  border-top: none;
  border-right: none;
  content: "";
  transform: rotate(-54deg) translateY(-50%);
}
.lp-widget-course-extra__content .course-extras.style-radios ul li,
.lp-widget-course-extra__content .course-extras.style-radios ol li {
  padding-left: 20px;
}
.lp-widget-course-extra__content .course-extras.style-radios ul li::before,
.lp-widget-course-extra__content .course-extras.style-radios ol li::before {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 0;
  width: 12px;
  height: 7px;
  border: 2px solid #8794b9;
  border-top: none;
  border-right: none;
  content: "";
  transform: rotate(-54deg) translateY(-50%);
}

.learnpress.widget_course_featured .lp-widget-featured-courses__footer,
.elementor-widget-wp-widget-learnpress_widget_course_featured .lp-widget-featured-courses__footer {
  margin-top: 20px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}
.learnpress.widget_course_featured .lp-widget-featured-courses__footer__link,
.elementor-widget-wp-widget-learnpress_widget_course_featured .lp-widget-featured-courses__footer__link {
  font-size: 1rem;
  line-height: 1;
}
.learnpress.widget_course_featured .lp-widget-featured-courses__footer__link::before,
.elementor-widget-wp-widget-learnpress_widget_course_featured .lp-widget-featured-courses__footer__link::before {
  content: "←";
}

.learnpress.widget_course_popular .lp-widget-popular-courses__footer,
.elementor-widget-wp-widget-learnpress_widget_course_popular .lp-widget-popular-courses__footer {
  margin-top: 20px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}
.learnpress.widget_course_popular .lp-widget-popular-courses__footer__link,
.elementor-widget-wp-widget-learnpress_widget_course_popular .lp-widget-popular-courses__footer__link {
  font-size: 1rem;
  line-height: 1;
}
.learnpress.widget_course_popular .lp-widget-popular-courses__footer__link::before,
.elementor-widget-wp-widget-learnpress_widget_course_popular .lp-widget-popular-courses__footer__link::before {
  content: "←";
}

.learnpress.widget_course_recent .lp-widget-recent-courses__footer,
.elementor-widget-wp-widget-learnpress_widget_course_recent .lp-widget-recent-courses__footer {
  margin-top: 20px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}
.learnpress.widget_course_recent .lp-widget-recent-courses__footer__link,
.elementor-widget-wp-widget-learnpress_widget_course_recent .lp-widget-recent-courses__footer__link {
  font-size: 1rem;
  line-height: 1;
}
.learnpress.widget_course_recent .lp-widget-recent-courses__footer__link::before,
.elementor-widget-wp-widget-learnpress_widget_course_recent .lp-widget-recent-courses__footer__link::before {
  content: "←";
}

.learnpress-widget-wrapper__restapi .lp-skeleton-animation {
  min-width: 250px;
}
.learnpress-widget-wrapper__restapi ul {
  padding: 0;
}