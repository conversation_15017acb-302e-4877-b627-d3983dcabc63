import*as lpEditCurriculumShare from"./share.js";import <PERSON><PERSON>lert from"sweetalert2";import Sortable from"sortablejs";const className={...lpEditCurriculumShare.className,elDivAddNewSection:".add-new-section",elSectionClone:".section.clone",elSectionTitleNewInput:".lp-section-title-new-input",elSectionTitleInput:".lp-section-title-input",elSectionDesInput:".lp-section-description-input",elBtnAddSection:".lp-btn-add-section",elBtnUpdateTitle:".lp-btn-update-section-title",elBtnUpdateDes:".lp-btn-update-section-description",elBtnCancelUpdateTitle:".lp-btn-cancel-update-section-title",elBtnCancelUpdateDes:".lp-btn-cancel-update-section-description",elBtnDeleteSection:".lp-btn-delete-section",elSectionDesc:".section-description",elSectionToggle:".section-toggle",elCountSections:".count-sections"};let{courseId:courseId,elEditCurriculum:elEditCurriculum,elCurriculumSections:elCurriculumSections,showToast:showToast,lpUtils:lpUtils,updateCountItems:updateCountItems}=lpEditCurriculumShare;const idUrlHandle="edit-course-curriculum",init=()=>{({courseId:courseId,elEditCurriculum:elEditCurriculum,elCurriculumSections:elCurriculumSections,showToast:showToast,lpUtils:lpUtils,updateCountItems:updateCountItems}=lpEditCurriculumShare)},addSection=(e,t)=>{let s=!1;if((t.closest(`${className.elBtnAddSection}`)||t.closest(`${className.elSectionTitleNewInput}`)&&"Enter"===e.key)&&(s=!0),!s)return;const l=t.closest(`${className.elDivAddNewSection}`);if(!l)return;e.preventDefault();const o=l.querySelector(`${className.elSectionTitleNewInput}`),c=o.value.trim(),i=o.dataset.messEmptyTitle;if(0===c.length)return void showToast(i,"error");o.value="",o.blur();const n=elCurriculumSections.querySelector(`${className.elSectionClone}`).cloneNode(!0);n.classList.remove("clone"),lpUtils.lpShowHideEl(n,1),lpUtils.lpSetLoadingEl(n,1);n.querySelector(`${className.elSectionTitleInput}`).value=c,elCurriculumSections.insertAdjacentElement("beforeend",n);const a={success:e=>{const{message:t,status:s,data:l}=e;if("error"===s)n.remove();else if("success"===s){const{section:e}=l;n.dataset.sectionId=e.section_id||""}showToast(t,s)},error:e=>{n.remove(),showToast(e,"error")},completed:()=>{lpUtils.lpSetLoadingEl(n,0),n.classList.remove(`${className.elCollapse}`);n.querySelector(`${className.elSectionDesInput}`).focus(),updateCountSections()}},r={action:"add_section",course_id:courseId,section_name:c,args:{id_url:idUrlHandle}};window.lpAJAXG.fetchAJAX(r,a)},deleteSection=(e,t)=>{const s=t.closest(`${className.elBtnDeleteSection}`);s&&SweetAlert.fire({title:s.dataset.title,text:s.dataset.content,icon:"warning",showCloseButton:!0,showCancelButton:!0,cancelButtonText:lpDataAdmin.i18n.cancel,confirmButtonText:lpDataAdmin.i18n.yes,reverseButtons:!0}).then((e=>{if(e.isConfirmed){const e=s.closest(".section"),t=e.dataset.sectionId;lpUtils.lpSetLoadingEl(e,1);const l={success:e=>{const{message:t,status:s}=e,{content:l}=e.data;showToast(t,s)},error:e=>{showToast(e,"error")},completed:()=>{lpUtils.lpSetLoadingEl(e,0),e.remove(),updateCountItems(e),updateCountSections()}},o={action:"delete_section",course_id:courseId,section_id:t,args:{id_url:idUrlHandle}};window.lpAJAXG.fetchAJAX(o,l)}}))},changeTitle=(e,t)=>{const s=t.closest(`${className.elSectionTitleInput}`);if(!s)return;const l=s.closest(`${className.elSection}`);s.value.trim()===(s.dataset.old||"")?l.classList.remove("editing"):l.classList.add("editing")},updateSectionTitle=(e,t)=>{let s=!1;if((t.closest(`${className.elBtnUpdateTitle}`)||t.closest(`${className.elSectionTitleInput}`)&&"Enter"===e.key)&&(s=!0),!s)return;e.preventDefault();const l=t.closest(`${className.elSection}`);if(!l)return;const o=l.querySelector(`${className.elSectionTitleInput}`);if(!o)return;const c=l.dataset.sectionId,i=o.value.trim(),n=o.dataset.old||"",a=o.dataset.messEmptyTitle;if(0===i.length)return void showToast(a,"error");if(i===n)return;o.blur(),lpUtils.lpSetLoadingEl(l,1);const r={success:e=>{const{message:t,status:s}=e;showToast(t,s),"success"===s&&(o.dataset.old=i)},error:e=>{showToast(e,"error")},completed:()=>{lpUtils.lpSetLoadingEl(l,0),l.classList.remove("editing")}},u={action:"update_section",course_id:courseId,section_id:c,section_name:i,args:{id_url:idUrlHandle}};window.lpAJAXG.fetchAJAX(u,r)},cancelSectionTitle=(e,t)=>{const s=t.closest(`${className.elBtnCancelUpdateTitle}`);if(!s)return;const l=s.closest(`${className.elSection}`),o=l.querySelector(`${className.elSectionTitleInput}`);o.value=o.dataset.old||"",l.classList.remove("editing")},updateSectionDescription=(e,t)=>{let s=!1;if((t.closest(`${className.elBtnUpdateDes}`)||t.closest(`${className.elSectionDesInput}`)&&"Enter"===e.key)&&(s=!0),!s)return;e.preventDefault();const l=t.closest(`${className.elSectionDesc}`);if(!l)return;const o=l.querySelector(`${className.elSectionDesInput}`);if(!o)return;const c=o.closest(`${className.elSection}`),i=c.dataset.sectionId,n=o.value.trim(),a=o.dataset.old||"";if(n===a)return;lpUtils.lpSetLoadingEl(c,1);const r={success:e=>{const{message:t,status:s}=e;showToast(t,s)},error:e=>{showToast(e,"error")},completed:()=>{lpUtils.lpSetLoadingEl(c,0);o.closest(`${className.elSectionDesc}`).classList.remove("editing"),o.dataset.old=n}},u={action:"update_section",course_id:courseId,section_id:i,section_description:n,args:{id_url:idUrlHandle}};window.lpAJAXG.fetchAJAX(u,r)},cancelSectionDescription=(e,t)=>{const s=t.closest(`${className.elBtnCancelUpdateDes}`);if(!s)return;const l=s.closest(`${className.elSectionDesc}`),o=l.querySelector(`${className.elSectionDesInput}`);o.value=o.dataset.old||"",l.classList.remove("editing")},changeDescription=(e,t)=>{const s=t.closest(`${className.elSectionDesInput}`);if(!s)return;const l=s.closest(`${className.elSectionDesc}`);s.value.trim()===(s.dataset.old||"")?l.classList.remove("editing"):l.classList.add("editing")},toggleSection=(e,t)=>{const s=t.closest(`${className.elSectionToggle}`);if(!s)return;const l=s.closest(`${className.elSection}`);l.closest(`${className.elCurriculumSections}`)&&(l.classList.toggle(`${className.elCollapse}`),checkAllSectionsCollapsed())},checkAllSectionsCollapsed=()=>{const e=elEditCurriculum.querySelectorAll(`${className.elSection}:not(.clone)`),t=elEditCurriculum.querySelector(`${className.elToggleAllSections}`);let s=!0;e.forEach((e=>{if(e.classList.contains(`${className.elCollapse}`))return s=!1,!1})),s?t.classList.remove(`${className.elCollapse}`):t.classList.add(`${className.elCollapse}`)},sortAbleSection=()=>{let e,t=0;new Sortable(elCurriculumSections,{handle:".drag",animation:150,onEnd:s=>{const l=s.item;if(!t)return;const o=l.closest(`${className.elSection}`),c=elCurriculumSections.querySelectorAll(`${className.elSection}`),i=[];c.forEach(((e,t)=>{const s=e.dataset.sectionId;i.push(s)}));const n={success:e=>{const{message:t,status:s}=e;showToast(t,s)},error:e=>{showToast(e,"error")},completed:()=>{lpUtils.lpSetLoadingEl(o,0),t=0}},a={action:"update_section_position",course_id:courseId,new_position:i,args:{id_url:idUrlHandle}};clearTimeout(e),e=setTimeout((()=>{lpUtils.lpSetLoadingEl(o,1),window.lpAJAXG.fetchAJAX(a,n)}),1e3)},onMove:t=>{clearTimeout(e)},onUpdate:e=>{t=1}})},updateCountSections=()=>{const e=elEditCurriculum.querySelector(`${className.elCountSections}`),t=elCurriculumSections.querySelectorAll(`${className.elSection}:not(.clone)`).length;e.dataset.count=t,e.querySelector(".count").textContent=t};export{init,addSection,deleteSection,changeTitle,updateSectionTitle,cancelSectionTitle,updateSectionDescription,cancelSectionDescription,changeDescription,toggleSection,sortAbleSection};