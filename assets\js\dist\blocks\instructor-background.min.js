(()=>{"use strict";const e=window.React,t=window.wp.i18n,r=window.wp.blockEditor,l=window.wp.components,a=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/instructor-background","title":"Instructor Background","category":"learnpress-course-elements","icon":"format-image","description":"Renders template Instructor Background PHP templates.","textdomain":"learnpress","keywords":["instructor background single","learnpress"],"ancestor":["learnpress/single-instructor"],"usesContext":[],"attributes":{"position":{"type":"string","default":"center"},"size":{"type":"string","default":"cover"},"repeat":{"type":"boolean","default":false}},"supports":{"inserter":true,"reusable":true,"reorder":true,"html":false,"multiple":false}}');(0,window.wp.blocks.registerBlockType)(a.name,{...a,edit:a=>{const n=(0,r.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(r.InspectorControls,null,(0,e.createElement)(l.PanelBody,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(l.SelectControl,{label:(0,t.__)("Background Position","learnpress"),value:a.attributes.position,options:[{label:"Center",value:"center"},{label:"Left",value:"left"},{label:"Right",value:"right"},{label:"Top",value:"top"},{label:"Bottom",value:"bottom"}],onChange:e=>a.setAttributes({position:e||""})}),(0,e.createElement)(l.SelectControl,{label:(0,t.__)("Background Size","learnpress"),value:a.attributes.size,options:[{label:"Auto",value:"auto"},{label:"Cover",value:"cover"},{label:"Contain",value:"contain"},{label:"Unset",value:"unset"}],onChange:e=>a.setAttributes({size:e||""})}),(0,e.createElement)(l.ToggleControl,{label:(0,t.__)("Background Repeat","learnpress"),checked:!!a.attributes.repeat,onChange:e=>{a.setAttributes({repeat:!!e})}}))),(0,e.createElement)("div",{...n},(0,e.createElement)("div",{className:"lp-user-cover-image_background"},(0,e.createElement)("img",{src:"https://placehold.co/1280x285?text=Background"}))))},save:e=>null})})();