import*as lpUtils from"../../utils.js";import Toastify from"toastify-js";import"toastify-js/src/toastify.css";let courseId,elEditCurriculum,elCurriculumSections,updateCountItems;const className={idElEditCurriculum:"#lp-course-edit-curriculum",elCurriculumSections:".curriculum-sections",elSection:".section",elToggleAllSections:".course-toggle-all-sections",elSectionItem:".section-item",LPTarget:".lp-target",elCollapse:"lp-collapse"},argsToastify={text:"",gravity:lpDataAdmin.toast.gravity,position:lpDataAdmin.toast.position,className:`${lpDataAdmin.toast.classPrefix}`,close:1==lpDataAdmin.toast.close,stopOnFocus:1==lpDataAdmin.toast.stopOnFocus,duration:lpDataAdmin.toast.duration},showToast=(t,s="success")=>{new Toastify({...argsToastify,text:t,className:`${lpDataAdmin.toast.classPrefix} ${s}`}).showToast()},setVariables=t=>{({courseId:courseId,elEditCurriculum:elEditCurriculum,elCurriculumSections:elCurriculumSections,updateCountItems:updateCountItems}=t)};export{setVariables,showToast,lpUtils,className,courseId,elEditCurriculum,elCurriculumSections,updateCountItems};